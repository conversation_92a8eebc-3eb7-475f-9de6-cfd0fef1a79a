spring.application.name=transaction-service
log.base.dir=/home/<USER>/deployment/logs/
server.port=8790
server.servlet.context-path=/${spring.application.name}
log.location=${log.base.dir}/${spring.application.name}/log
spring.mvc.servlet.path=/rest

environment.type=SPROD


javamelody.management-endpoint-monitoring-enabled=true

wallet.decision.path=file:/data/app/kettle/sprod/drools/wallet_decision/wallet_decision.xls
denomination.decision.path=file:/data/app/kettle/sprod/drools/denomination_decision/denomination_decision.xls

#MASTER_CONFIG
master.jdbc.driver-class=com.mysql.cj.jdbc.Driver
master.jdbc.url=***********************************************************************************************************
master.jdbc.user-name=chaayosprodusr
master.jdbc.password=ch44Y05Pr0dU53r

#KETTLE_CONFIG
kettle.jdbc.driver-class=com.mysql.cj.jdbc.Driver
kettle.jdbc.url=****************************************************************************************************
kettle.jdbc.user-name=chaayosprodusr
kettle.jdbc.password=ch44Y05Pr0dU53r

#KETTLE_CLM
clm.jdbc.driver-class=com.mysql.cj.jdbc.Driver
clm.jdbc.url=*****************************************************************************
clm.jdbc.user-name=rptusr
clm.jdbc.password=321In#@!


#HIBERNATE
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show-sql=false
hibernate.hbm-to-ddl-auto=validate


springdoc.api-docs.enabled=true

env.type=SPROD

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

logging.file.path=kettle-service/logs



spring.security.user.name=root
spring.security.user.password=kettle123!@#

chaayos.base.url=https://cafes.chaayos.com



add.wastage.url=https://relax.chaayos.com/scm-service/rest/v1/stock-management/kettle-wastage-event
verify.price.data=https://relax.chaayos.com/scm-service/rest/v1/stock-management/verify-price-data


employee.meal.amount.limit=45
employee.meal.day.limit=26
employee.meal.monthly.start.date=25
cash.back.start.date=2021-03-01
cash.back.end.date=2021-12-31
cash.back.percentage=0
mail.dummy.customer.id=1
server.base.dir=/data/app/kettle/sprod
inventory.track=true

inventory.base.url=https://relax.chaayos.com

kettle.create.order.url=https://relax.chaayos.com/kettle-service/rest/v1/order-management/create-order-result
kettle.auth.internal=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImtldHRsZS1zZXJ2aWNlIiwiZW52VHlwZSI6IlNQUk9EIiwicGFydG5lcklkIjoyNiwicGFzc0NvZGUiOiI4VExNUEZDNjJKMzk4SEciLCJpYXQiOjE2ODI5MjcwMTR9.7V_We1h03gOFvvJmBQZ360XAov8BN5GxR0xQVjYWvK0

interceptor.secure.url=/**
interceptor.open.url=/v2/external/**,/actuator/**,/user/lookup,/user/signup,/user/login,/error/*,/error,/*/*.html,/*.html,/v3/api-docs,/v3/api-docs/*,/swagger-ui/*,/admin/login,/user/forgot-pin,/user/reset-pin 

run.validate.filter=true

raw.print.enabled=true

order.feedback.type=internal
subscription.valid.buy.n.days.value=31
send.automated.delivery.nps.sms=false
send.feedback.message.delivery.swiggy=true
dinein.post.order.offer.enabled=true
delivery.post.order.offer.enabled=true
dine.post.order.offer.check.last.n.days.value=30
#delivery.post.order.offer.check.last.n.days.value=30 not used

cash.back.card.start.date=2020-12-28
cash.back.card.end.date=2021-01-28
cash.card.payment.mode.id=10

clevertap.accountId=W48-5Z5-466Z
clevertap.passcode=IAE-JOZ-AEUL
clevertap.push.enable=true



facebook.push.enable=true
send.cod.order.feedback.message=false
system.generated.notifications=false

mail.receipt.email=<EMAIL>
send.automated.nps.sms=true

promotional.offer.active=true
promotional.offer.start.date=2019-10-02
promotional.offer.end.date=2019-12-31
promotional.offer.html.text=<span style="font-size: 18px; text-align:center; line-height: 30px;">Flat 40% off on <a href="https://bit.ly/oyochaayos"><img src="https://cafes.chaayos.com/oyo-logo.png" style="margin-bottom: -10px;" title="OYO" width="40px"></a> bookings | Use code: <a href="https://bit.ly/oyochaayos"><b>OYOCHA</b></a> | Valid for 2 Bookings till 31st Dec 2019 </span>

amazon.s3.bucket=chaayos
aws.s3.accessKey=********************
aws.s3.secretKey=waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY

order.receipt.cloudfront=https://d1frqtha8rm08n.cloudfront.net

is.show.nps.rating=true
automated.feedback.sms.dinein.time.gap=45
automated.feedback.sms.delivery.time.gap=120
automated.feedback.sms.next.day.time.gap=540
automated.feedback.sms.threshold.time.gap=1260
automated.nps.sms.threshold.time.gap=180
automated.nps.sms.dinein.time.gap=45
automated.nps.sms.delivery.time.gap=80
send.automated.otp.sms=true
dinein.order.notification.whatsapp = false
send.campaign.sms.by.system=true

automated.feedback.sms.trigger.for.all=false



micro.wallet.id=2
direct.wallet.id=3

is.client.node=true
client.node.ip.details=*********
kettle.client.node.ip.details=



order.info.queue.region=AP_SOUTH_1
offer.drools.s3.bucket=com.chaayos.drool.prod

