spring.application.name=transaction-service

server.port=8790
server.servlet.context-path=/${spring.application.name}
spring.mvc.servlet.path=/rest

environment.type=STAGE

wallet.decision.path=file:/data/app/kettle/stage/drools/wallet_decision/wallet_decision.xls
denomination.decision.path=file:/data/app/kettle/stage/drools/denomination_decision/denomination_decision.xls

#spring.datasource.url=**********************************************************************
#spring.datasource.username=root
#spring.datasource.password=Chaayos123#@!
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.hikari.username=root
#spring.datasource.hikari.password=Chaayos123#@!
#spring.datasource.hikari.jdbc-url=**********************************************************************
#spring.datasource.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.hibernate.ddl-auto=validate
#spring.jpa.show-sql=false
#spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.properties.hibernate.generate_statistics=true

#MASTER_CONFIG
master.jdbc.driver-class=com.mysql.cj.jdbc.Driver
master.jdbc.url=*********************************************************************************
master.jdbc.user-name=root
master.jdbc.password=Chaayos123#@!

#KETTLE_CONFIG
kettle.jdbc.driver-class=com.mysql.cj.jdbc.Driver
kettle.jdbc.url=**************************************************************************
kettle.jdbc.user-name=root
kettle.jdbc.password=Chaayos123#@!

#KETTLE_CLM
clm.jdbc.driver-class=com.mysql.cj.jdbc.Driver
clm.jdbc.url=*****************************************************************************
clm.jdbc.user-name=rptusr
clm.jdbc.password=321In#@!


#HIBERNATE
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show-sql=true
hibernate.hbm-to-ddl-auto=validate


springdoc.api-docs.enabled=true

env.type=stage

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

logging.file.path=kettle-service/logs

ignite.server.ip.address = *************:47500..47509

spring.security.user.name=root
spring.security.user.password=kettle123!@#


verify.price.data=http://************/scm-service/rest/v1/stock-management/verify-price-data
add.wastage.url=http://************/scm-service/rest/v1/stock-management/kettle-wastage-event


employee.meal.amount.limit=45
employee.meal.day.limit=26
employee.meal.monthly.start.date=25
cash.back.start.date=2021-03-01
cash.back.end.date=2021-12-31
cash.back.percentage=0
mail.dummy.customer.id = 5
server.base.dir=/data/app/kettle/stage
inventory.track=true

inventory.base.url=http://************:8787

interceptor.secure.url=/**
interceptor.open.url=/v2/external/**,/actuator/**,/user/lookup,/user/signup,/user/login,/error/*,/error,/*/*.html,/*.html,/v3/api-docs,/v3/api-docs/*,/swagger-ui/*,/admin/login,/user/forgot-pin,/user/reset-pin 

chaayos.base.url=https://cafes.chaayos.com

run.validate.filter=true

raw.print.enabled=true


order.feedback.type=internal
subscription.valid.buy.n.days.value=31
send.automated.delivery.nps.sms=true
send.feedback.message.delivery.swiggy=false
dinein.post.order.offer.enabled=true
delivery.post.order.offer.enabled=true
dine.post.order.offer.check.last.n.days.value=30

cash.back.card.start.date=2021-03-01
cash.back.card.end.date=2021-03-01
cash.card.payment.mode.id=10

clevertap.accountId=TEST-84W-68Z-966Z
clevertap.passcode=SAW-SQD-WEUL
clevertap.push.enable=true

facebook.push.enable=false

send.cod.order.feedback.message=true
system.generated.notifications=false
send.automated.nps.sms=true
mail.receipt.email=<EMAIL>


promotional.offer.active=true
promotional.offer.start.date=2019-07-20
promotional.offer.end.date=2019-08-31
promotional.offer.html.text=<span style="font-size: 18px; text-align:center; line-height: 30px;">Flat 35% off on <a href="https://bit.ly/oyochaayos"><img src="https://cafes.chaayos.com/oyo-logo.png" style="margin-bottom: -10px;" title="OYO" width="40px"></a> bookings | Use code: <a href="https://bit.ly/oyochaayos"><b>OYOCHA</b></a> | Valid for 2 Bookings till 31st Aug 2019 </span>

amazon.s3.bucket=chaayosdevtest
aws.s3.accessKey=********************
aws.s3.secretKey=waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY

order.receipt.cloudfront=https://d3pcv1qlege227.cloudfront.net

is.show.nps.rating=true
automated.feedback.sms.threshold.time.gap=1260
automated.nps.sms.delivery.time.gap=2
automated.nps.sms.dinein.time.gap=2
automated.feedback.sms.delivery.time.gap=2
automated.feedback.sms.dinein.time.gap=2
automated.feedback.sms.next.day.time.gap=2
send.automated.otp.sms=true
dinein.order.notification.whatsapp =true
send.campaign.sms.by.system=true






micro.wallet.id=2
direct.wallet.id=3

server.node.type = false
server.node.ip.details=********:5701,*********:5701,*********:5701

kettle.server.node.cluster.ip=********:6701,*********:6701,*********:6701

order.info.queue.region=EU_WEST_1

send.feedback.sms.from.clevertap=false

clevertap.log.enable=true