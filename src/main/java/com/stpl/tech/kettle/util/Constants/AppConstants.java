package com.stpl.tech.kettle.util.Constants;

import com.stpl.tech.master.domain.model.UnitCategory;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

public class AppConstants {
	/**
	 * Authentication
	 */
	public static final String DATE_FORMAT = "yyyy-MM-dd";
	public static final String CHAAYOS_BRAND = "Chaayos";
	public static final String GNT_BRAND = "G&T";
	public static final String ALGORITHM = "AES";
	public static final String PASSPHRASE_KEY = "C4@@y05a^3)H-5uN";

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern(DATE_FORMAT);
	/**
	 * Date related
	 */
	public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormat.forPattern("yyyy-MM");
	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_WITH_NO_MILISECOND_FORMATTER = DateTimeFormat
			.forPattern("yyyy-MM-dd HH:mm:ss");

	public static final DateTimeFormatter DATE_TIME_WITH_ONLY_TIME_FORMATTER = DateTimeFormat.forPattern("HH:mm");
	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_TIME_FORMATTER_WITH_NO_CHARACTERS = DateTimeFormat
			.forPattern("yyyyMMddHHmmss");

	/**
	 * Delivery related
	 */
	public static final DateTimeFormatter DATE_TIME_ISO_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mmZ");
	public static final DateTimeFormatter DATE_TIME_ISO_FORMATTER_IN_SEC = DateTimeFormat
			.forPattern("yyyy-MM-dd'T'HH:mm:ssZ");

	public static final String ISO_DATETIME_WITH_TIMEZONE = "yyyy-MM-dd'T'HH:mm:ss.SSSX";

	public static final String BILL_PRINT_DATE_FORMAT = "dd/MM/yyyy HH:mm";
	public static final String BILL_PRINT_TIME_FORMAT = "HH:mm:ss";
	/**
	 * Ref lookup related
	 */
	public static final String RTL_GROUP_CATEGORY = "CATEGORY";
	public static final String RTL_GROUP_DIMENSION = "DIMENSION";
	public static final String RTL_GROUP_ITEM_PER_TICKET = "ITEM_PER_TICKET";
	public static final String RTL_GROUP_ADDONS = "ADDONS";
	public static final String RTL_GROUP_DISCOUNT = "DISCOUNT";
	public static final String RTL_GROUP_COMPLIMENTARY = "COMPLIMENTARY";
	public static final String RTL_GROUP_WEB_CATEGORY = "WEB_CATEGORY";
	public static final String RTL_GROUP_PR_TYPE = "PR_TYPE";
	public static final String RTL_GROUP_ADJUSTMENT_COMMENT = "ADJUSTMENT_COMMENT ";


	public static final String RTL_CODE_DISCOUNT_CODE = "DiscountCode";
	public static final String RTL_CODE_CORE_PRODUCTS = "CORE_PRODUCTS";
	public static final String RTL_CODE_CATEGORY_PRODUCTS = "CATEGORY_PRODUCTS";
	public static final String RTL_CODE_COMPLIMENTARY_CODE = "ComplimentaryCode";

	public static final String RTL_CODE_DEFAULT_DISCOUNT_CODE = "Other";
	public static final String RTL_CODE_DEFAULT_COMPLIMENTARY_CODE = "Any Other";
	public static final String RTL_GROUP_CATEGORY_COMBO_NAME = "Combos";

	/**
	 * Constants
	 */
	public static final String YES = "Y";
	public static final String NO = "N";
	public static final String ALL = "All";
	public static final int GET_FOR_ALL = -1;

	/**
	 * Defaults
	 */

	public static final String DEFAULT_HAS_PARCEL = NO;
	public static final String DEFAULT_IS_COMPLIMENTARY = NO;
	public static final String DEFAULT_IS_NUMBER_VERIFIED = NO;
	public static final String DEFAULT_IS_EMAIL_VERIFIED = NO;
	public static final String DEFAULT_COUNTRY_CODE = "+91";
	public static final String DEFAULT_ADDRESS_TYPE = "RESEDENTIAL";
	public static final String DEFAULT_HAS_MULTIPLE_SECTION = NO;
	public static final String DEFAULT_AUTO_TRIGGERED = YES;
	public static final String DEFAULT_PASSCODE = "123123";
	public static final String OTHERS = "others";
	public static final int APP_BUILD_SEED_VERSION = 50;
	public static final String ASSEMBLY_ORDER_CHANNEL = "_assemblyorderchannel_";
	public static final String CANCELLED = "CANCELLED";
	public static final String SWIGGY = "SWIGGY";
	public static final String ZOMATO = "ZOMATO";
	public static final String BOTH_SWIGGY_ZOMATO = "BOTH";
	public static final String WA_OPT_IN = "OPT_IN";

	public static final String INTRA_STATE = "INTRA_STATE";
	public static final String INTER_STATE = "INTER_STATE";
	public static final String IGST = "IGST";

	public static final String updateStatus = "UPDATED";
	public static final String saveStatus = "SAVED";
	public static final String markedInactive = "MARKED_INACTIVE";
	public static final String FAILED = "FAILED";
	public static final String DINE_IN_MILK_SELECTION_VARIANT_NAME = "MILK SELECTION";
	public static final String DEFAULT_UNIT_ZONE = "north";
	public static final String TEST_CONTACT_NUMBER = "9818019762";
	public static final String CHARITY_ORDER_NOTIFICATION_NUMBER = "9717779785";
	public static final String PRODUCT_SOURCE_SYSTEM_OPTION = "OPTION";
	public static final String REVALIDATION_SUCCESSFUL = "REVALIDATION_SUCCESSFUL";

	public static final int SCM_MILK_PRODUCT_ID = 100234;
	public static String addons = "Addons";

	public static final String getValue(String value, String defaultIfNull) {
		return value == null || "".equals(value.trim()) ? defaultIfNull : value;
	}

	public static final String getValue(Boolean value) {

		return value != null && value ? YES : NO;
	}

	public static final boolean getValue(String value) {

		return YES.equals(value);
	}

	/**
	 * App Constants
	 */

	public static final int DINE_IN_CHANNEL_PARTNER = 1;
	public static final int DINE_IN_CHANNEL_PARTNER_CODE = 21;
	public static final int NO_DIMENSION_CODE = 1;
	public static final String NO_DIMENSION_STRING = "None";
	public static final int CAFE_VISIT_LOYALTY_POINT = 10;
	public static final int VERIFICATION_LOYALTY_POINT = 60;
	public static final int EMOTION_CONFIDENCE_THRESHOLD = 80;
	public static final int CUTOFF_LOYALTY_POINTS = 300;
	public static final String PARCEL_CODE = "Parcel";
	public static final MathContext MATH_CONTEXT = new MathContext(10);
	public static final String CHAAYOS_RECEIPT = "Chaayos Receipt";
	public static final String CHAAYOS_SUBSCRIPTION = "Chaayos Subscription";
	public static final String CHAI_PREPAID = "CHAI_PREPAID";
	public static final String CHAAYOS_DELIVERY = "Delivery Support";
	public static final String DELIVERY = "DELIVERY";
	public static final String TOTAL_SALE = "TOTAL_SALE";
	public static final String DINE_IN = "DINE_IN";
	public static final String CHAAYOS_COD = "Chaayos-COD";
	public static final String MARKETING_PARTNER = "Marketing-Partner";
	public static final int CHAAYOS_COMBO_PRODUCT_TYPE = 8;
	public static final Set<Integer> ADMIN_DEPARTMENTS = new HashSet<Integer>(Arrays.asList(102, 105));
	public static final int ADMIN_USER_ID = 100000;
	public static final int EMPLOYEE_MEAL_ID = 2100;
	public static final int SAMPLING_AND_MARKETING = 2106;

	public static boolean isDineIn(int channelPartnerId) {
		return channelPartnerId == DINE_IN_CHANNEL_PARTNER || channelPartnerId == DINE_IN_CHANNEL_PARTNER_CODE;
	}

	public static final String OFFER_SCOPE_CORPORATE = "CORPORATE";
	public static final String CAFE_BILL_PRINT_TEMPLATE = "template/OrderPrint.html";
	public static final String COD_BILL_PRINT_TEMPLATE = "template/OrderCODPrint.html";

	// Web Socket based configurations
	public static final String WEB_SOCKET_CHANNEL = "/ws-channel/";
	public static final String WEB_SOCKET_CHANNEL_ORDERS = "/orders";
	public static final String ORDER_QUEUE_CHANNEL = "/order-queue";
	public static final String UPDATE_ORDERS = "/updateOrder";

	public static final String EXCEL_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
	public static final String TEXT_MIME_TYPE = "text/html";
	public static final String CSV_MIME_TYPE = "text/csv";
	public static final String ZIP_MIME_TYPE = "application/zip";
	public static final String PDF_MIME_TYPE = "application/pdf";
	public static final String JSON_MIME_TYPE = "application/json";
	public static final String EXCEL_MIME_TYPE_XLS = "application/xls";
	public static final String JPEG_MIME_TYPE = "image/jpeg";
	public static final String JPG_MIME_TYPE = "image/jpg";
	public static final String PNG_MIME_TYPE = "image/png";

	/**
	 * Unit Status
	 */
	public static final String ACTIVE = "ACTIVE";
	public static final String IN_ACTIVE = "IN_ACTIVE";
	public static final String PENDING_REQUEST = "PENDING";
	public static final String COMPLETED_REQUEST = "COMPLETED";
	public static final String APPROVED_REQUEST = "APPROVED";
	public static final String INTERNAL_SOURCE = "INTERNAL SOURCE";
	public static final String ARCHIVED = "ARCHIVED";
	public static final String APPROVED = "APPROVED";

	public static final String PAYMENT_MODE_CREDIT = "Credit";

	/**
	 * Complimentary Eligibility
	 */
	public static final String ACCOUNTABLE = "ACCOUNTABLE";
	public static final String NOT_ACCOUNTABLE = "NOT_ACCOUNTABLE";

	// public static final int DELIVERY_REQUEST_API_TIMEOUT = 10;

	public static final int DELIVERY_PARTNER_AUTOMATED = -1;

	public static final int DELIVERY_PARTNER_PICKUP = 5;

	public static final int DELIVERY_PARTNER_NONE = 1;

	public static final int DELIVERY_PARTNER_CHAAYOS_DELIVERY = 8;

	public static final int TAKEAWAY_SEED = 100;

	/** Email of the Service Account */
	public static final String SERVICE_ACCOUNT_ACCOUNT_EMAIL = "<EMAIL>";

	/*
	 * public static final String MIMETYPE_GOOGLE_SHEETS =
	 * "application/vnd.google-apps.spreadsheet"; public static final String
	 * PREFIX_EXPENSE_REPORT = "expense"; public static final String
	 * PREFIX_EXPENSE_REPORT_OUTPUT = "expense_output"; public static final String
	 * APP_NAME_EXPENSE_REPORT = "scoreboard";
	 */

	public static final int COMPLEMENTARY_CODE_COMBO = 1;
	public static final int COMPLEMENTARY_CODE_LOYALTY = 2101;

	public static final String DIMENSION_NONE = "None";

	public static final int DEPARTMEMT_CORPORATE_ID = 102;
	public static final int DESIGNATION_ADMIN_ID = 1009;

	public static final int PAYMENT_MODE_CASH = 1;
	public static final int PAYMENT_MODE_GIFT_CARD = 10;
	public static final int PAYMENT_MODE_DINE_IN_CREDIT = 21;
	public static final int PAYMENT_MODE_AMEX = 3;

	public static final int CHAI_ON_DEMAND_UNIT_ID = 11001;
	public static final String CAFE = "CAFE";
	public static final String COD = "COD";
	public static final String TAKE_AWAY = "TAKE_AWAY";
	public static final String NEO = "NEO";
	public static final String HYPHEN = "-";
	public static final int CHANNEL_PARTNER_WEB_APP = 14;
	public static final int ZERO = 0;
	public static final int ONE = 1;

	public static final int SYSTEM_EMPLOYEE_ID = 120056;
	public static final String SYSTEM_EMPLOYEE_NAME = "System";

	public static final String ETKN = "ETKN";

	public static final String KETTLE = "Kettle";

	public static final Integer[] SET_VALUES = new Integer[] { 1000007, 1043, 1044, 1027, 1026, 1048, 861 };
	public static final Set<Integer> HIDDEN_PRODUCTS_SET = new HashSet<>(Arrays.asList(SET_VALUES));

	public static final Integer[] ZERO_TAX_PRODUCTS_ID = new Integer[] { 1043, 1044 };
	public static final Set<Integer> ZERO_TAX_PRODUCTS_SET = new HashSet<>(Arrays.asList(ZERO_TAX_PRODUCTS_ID));

	public static final String CHARSET = "UTF-8";

	public static final int CATEGORY_OTHERS = 12;

	public static final int CATEGORY_MERCHANDISE = 9;

	public static final String TAX_EXEMPTED = "TAX_EXEMPTED";

	public static final String GIFT_CARD_TAX_CODE = "GIFT_CARD";
	public static final String WALLET_TYPE = "WALLET_TYPE";
	public static final String SUGGEST_WALLET = "SUGGEST_WALLET";
	public static final String GIFT_CARD_WALLET = "GIFT_CARD_WALLET";
	public static final String PRODUCT_ATTRIBUTE_NON_VEG = "NON_VEG";
	public static final String COMBO_TAX_CODE = "COMBO";
	public static final String ZERO_TAX_CODE = "ZERO_TAX";
	public static final int GST_TAX_ID = 1;
	public static final int COUNTRY_ID_INDIA = 1;

	public static final String ORDER_TYPE_REGULAR = "order";
	public static final String ORDER_TYPE_TABLE_ORDER = "table_order";

	public static final String ORDER_TYPE_COMPLIMENTARY_GIFTCARD = "complimentary-gift-card";
	public static final String ORDER_TYPE_EMPLOYEE_MEAL = "employee-meal";
	public static final String ORDER_TYPE_PAID_EMPLOYEE_MEAL = "paid-employee-meal";
	public static final String ORDER_TYPE_COMPLIMENTARY = "complimentary-order";
	public static final String ORDER_TYPE_UNSTAISFIED_CUSTOMER = "unsatisfied-customer-order";
	public static final String ORDER_TYPE_TESTING_ORDER = "test-order";

	public static final String REPORT_FREQUENCY = "FREQUENCY";
	public static final String BLANK = "";
	public static final String SFTP = "SFTP";

	public static final String DIMESION_FULL_STRING = "Full";
	public static final String DIMESION_REGULAR_STRING = "Regular";

	public static final String BUDGET_UNAVAILABLE = "BUDGET_NOT_PROVIDED";

	public static final String ZTZ = "ZTZ";

	public static final String AM_RESPONSE = "amResponse";
	public static final String DGM_RESPONSE = "dgmResponse";

	public static final String RECIPE_PROFILE_P0 = "P0";
	public static final String RECIPE_PROFILE_P1 = "P1";
	public static final String RECIPE_PROFILE_OP1 = "O_P1";
	public static final String RECIPE_PROFILE_P2 = "P2";
	public static final String RECIPE_PROFILE_P3 = "P3";
	public static final String RECIPE_PROFILE_CC1 = "CC1";

	public static final String RECIPE_PROFILE_OP0 = "O_P0";
	public static final String RECIPE_PROFILE_OP2 = "O_P2";
	public static final String RECIPE_PROFILE_OP3 = "O_P3";
	public static final String RECIPE_PROFILE_OCC1 = "O_CC1";
	public static final String RECIPE_PROFILE_DC0 = "DC0";
	public static final String RECIPE_PROFILE_ODC0 = "O_DC0";
	public static final String RECIPE_PROFILE_PP2 = "PP2";
	public static final String RECIPE_PROFILE_OPP2 = "O_PP2";
	public static final String RECIPE_PROFILE_PP3 = "PP3";
	public static final String RECIPE_PROFILE_OPP3 = "O_PP3";
	public static final String RECIPE_PROFILE_PC1 = "PC1";
	public static final String RECIPE_PROFILE_OPC1 = "O_PC1";

	public static final String DEFAULT_RECIPE_PROFILE = RECIPE_PROFILE_P0;
	public static final String ORDER_CANCELLATION = "OrderCancellation";
	public static final String FORWARD_SLASH = "/";
	public static final int CHANNEL_PARTNER_ZOMATO = 3;
	public static final int CHANNEL_PARTNER_SWIGGY = 6;
	public static final String BOOLEAN = "boolean";
	public static final int CHANNEL_PARTNER_DINE_IN_APP = 21;
	public static final String CALL_BACK_QUESTION = "call you back";

	public static final String PERCENTAGE = "PERCENTAGE";
	public static final String FIXED = "FIXED";
	public static final String BATCH_CODE_KEY_TYPE_UNIT = "UNIT";
	public static final String BATCH_CODE_KEY_TYPE_SKU = "SKU";

	// TODO Vivek Move this to metadata
	public static final String CHAAYOS_CASH_OFFER_CODE = "CHAAYOS_CASH";
	public static final String CHAAYOS_CASH_BACK_OFFER_CODE = "CASHBACK";

	public static final  String DOHFUL_CASH_OFFER_CODE = "DOHFUL_CASH";
	public static final BigDecimal CHAAYOS_CASH_REDEMPTION_PER_ORDER = new BigDecimal(100);
	public static final int CHAAYOS_CASH_OFFER_REDEMPTION_DAYS = 0;
	public static final int CHAAYOS_CASH_OFFER_MAX_ALLOWED_REDEMPTION = 1;

	public static final String CHAAYOS_SELECT_SUBSCRIPTION_OFFER_CODE = "CHAAYOS_SELECT";
	public static final String CHAAYOS_PRO_SUBSCRIPTION_OFFER_CODE = "CHAAYOS_PRO";

	public static final String TRUE_CALLER = "TRUE_CALLER";

	public static final int CHAAYOS_BRAND_ID = 1;
	public static final int DOHFUL_BRAND_ID = 6;

	public static final int DESI_CANTEEN_BRAND_ID = 2;
	public static final int SWIGGY_CAFE_BRAND_ID = 5;

	public static final int GNT_BRAND_ID = 3;

	public static final int CHAAYOS_DINEIN_PARTNER_ID = 1;

	public static final int CHAAYOS_DELIVERY_PARTNER_ID = 2;

	public static final String ASSET_DEPRECIATION_STRATEGY = "FLAT_LINE";
	public static final int UPDATE_INVENTORY_SUCCESS = 1;
	public static final int UPDATE_INVENTORY_FAIL = -1;
	public static final int UPDATE_INVENTORY_INIT = 0;

	public static final String S3_REPORT_BASE_PATH = "reports";
	public static final String S3_REPORT_VERSION_FOLDER = "TEST_REPORT_VERSION";
	public static final String MAPPING_SOURCE_COUPON = "COUPON";
	public static final String MAPPING_SOURCE_OFFER = "OFFER";
	public static final String CHANNEL_PARTNER_DEFAULT_COUPON = "ZOMATO";

	public static final List<Integer> PROMOTER_RATINGS = Arrays.asList(9, 10);

	public static final String REMAINING_DAY = "REMAINING_DAY";
	public static final String ORDERING_DAY = "ORDERING_DAY";

	public static final int PSEUDO_GIFT_CART_ID = 1;
	public static final int ADVANCE_PAYMENT_PRODUCT_ID = 3;

	public static final String ABOUT_TO_STOCK_OUT = "1";
	public static final String STOCK_OUT = "0";
	public static final String SIGNUP_OFFER_DELIVERY = "SIGNUP_OFFER_DELIVERY";
	// MAC
	public static final String ACCEPTED = "ACCEPTED";
	public static final String REJECTED = "REJECTED";
	public static final Integer ORDER_CANCELLATION_THRESHOLD = 60; // second

	public static final String BULK_ADJUSTMENT = "BULK_ADJUSTMENT";

	public static final String COMPANY_SHORT_NAME = "STPL";

	public static final int BAZAAR_PARTNER_ID = 23;
	public static final int BAZAAR_UNIT_ID = 26254;

	public static final String LOYALTY_GIFTING = "GIFTING";

	public static final Integer PROMOTER = 9;
	public static final Integer PASSIVE = 8;
	public static final Integer FEEDBACK_PROMOTER = 4;
	public static final Integer FEEDBACK_PASSIVE = 3;
	// public static final BigDecimal DETRACTOR= new BigDecimal(9);
	public static final List<Integer> DESI_CHAI_ID = Arrays.asList(10, 11, 12, 14, 15, 50);
	public static final List<Integer> BAARISH_CHAI_ID = Arrays.asList(1282, 1292, 1293, 1294);
	public static final List<String> ACTIVE_EMPLOYEE_APPLICATIONS = Arrays.asList("KETTLE_SERVICE", "KETTLE_CRM");
	public static final String DESI_CHAI = "Desi Chai";
	public static final String BAARISH_CHAI = "Baarish Wali Chai";

	public static final String STATUS_CREATED = "CREATED";
	public static final String STATUS_NOTIFIED = "NOTIFIED";
	public static final String STATUS_SUCCESSFUL = "SUCCESSFUL";

	public static final List<Integer> EXCLUDE_CUSTOMER_IDS = Arrays.asList(1, 2, 3, 4, 5, 24035, 67456, 142315, 24035,
			142315, 3527255);

	public static final Set<Integer> SEASONAL_PRODUCT_IDS_1 = new HashSet<Integer>(
			Arrays.asList(1000065, 1000080));/* Anda Parantha and Dhokla */
	public static final Set<Integer> SEASONAL_PRODUCT_IDS_2 = new HashSet<Integer>(
			Arrays.asList(1000084, 1000085));/* Veg puff and chicken puff */
	public static final Set<Integer> SEASONAL_PRODUCT_IDS_3 = new HashSet<Integer>(
			Arrays.asList(1000066));/* Kesar Chai */
	public static final int SYSTEM_UNIT_ID = 0;

	public static final Pattern VALID_EMAIL_ADDRESS_REGEX = Pattern.compile("^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,6}$",
			Pattern.CASE_INSENSITIVE);

	public static final String STOCK_IN_CONSTANT = "STOCK_IN";
	public static final String STOCK_OUT_CONSTANT = "STOCK_OUT";
	public static final String INITIATED = "INITIATED";
	public static final String FIXED_ASSETS = "FA_Equipment";
	public static final String FIXED_ASSET_ORDER = "FIXED_ASSET_ORDER";
	public static final String GOODS = "NRE_Consumable";
	public static final String NEXT_BEST_OFFER = "NEXT_BEST_OFFER";
	public static final String LOYAL_TEA_COUPON_CODE = "LOYAL_TEA";
	public static final Integer NEXT_BEST_OFFER_CAMPAIGN_ID = 1;

	public static final String MD5_HASH = "MD5";
	public static final String SHA256_HASH = "SHA-256";

	public static final String FACEBOOK = "FB";
	public static final String GOOGLE = "GOOGLE";
	public static final String CLEVERTAP = "CLV";

	public static final Integer DELIVERY_PRODUCT_ID = 1044;
	public static final Integer PACKAGING_PRODUCT_ID = 1043;

	public static final String marketingImage = "MARKETING_IMAGES_MOB_VIEW";
	public static final String marketingImageWebView = "MARKETING_IMAGES_WEB_VIEW";

	public static final BigDecimal UNIT_PRODUCT_PRICE_THRESHOLD = BigDecimal.valueOf(0.20);

	public static final String SMS = "SMS";
	public static final String PREFERENCE = "PREFERENCE";

	public static final String APP = "APP";

	public static final String F9_ORDERING = "FOUNTAIN9_DATA_SOURCE_ORDERING";

	public static final String INGREDIENT = "INGREDIENT";

	public static final String OVER_ALL = "OVER_ALL";
	public static final String REGION = "REGION";
	public static final String CITY = "CITY";
	public static final int SUBSCRIPTION_PRODUCT_SUB_CATEGORY = 3810;
	public static final String ERROR = "ERROR";

	// Type common with bakery merchendise.
	public static final List<Integer> CLEVERTAP_FOOD_PRODUCT_IDS = Arrays.asList(780, 790, 800, 810, 865, 1003, 1004,
			1005, 1006, 1007, 1145, 1146, 1147, 1182, 1183, 1238, 1239, 1586, 1587, 1588, 1589, 1590);
	public static final List<Integer> CLEVERTAP_FOOD_PRODUCT_TYPE = Arrays.asList(7, 10, 52, 53, 60, 61);
	public static final List<String> IGNORE_UNIT_KEYWORDS = Arrays.asList("odc", "zepto", "zomato", "bazaar", "test",
			"zomaro", "bots");

	public static final String KNOCK_NOTIFICATION_ENDPOINT = "rest/notification/";

	public static final Integer WS_MANUAL_TASK_ORDER_TIME_DIFF = 1;

	public static final String AGREEMENT_TYPE_VENDOR = "VENDOR";
	public static final String CREATED_VENDOR = "CREATED";
	public static final String EDITED_VENDOR = "EDITED";

	public static final String CONACT_NUMBER = "contactNumber";

	public static final String EMAIL_ID = "emailId";

	public static final String CONACT_NUMBER_DATA = "contactNumberData";

	public static final String EMAIL_ID_DATA = "emailIdData";

	public static final String CUSTOMER_INFO = "CustomerInfo";

	public static final String CUSTOMER_LEAD_SOURCES = "FB_LEADGEN,GOOGLE,CHAAYOS-COD,NEO_SERVICE,FACEBOOK,NEWSPAPER,INSTAGRAM,CLM,WHATSAPP,SHOPIFY,CONTLO_SHOPIFY";

	public static final String CUSTOMER_CONTACT_INFO_MAPPING_OLD = "CustomerContactInfoMappingOld";

	public static final String CUSTOMER_CONTACT_INFO_MAPPING = "CustomerContactInfoMapping";

	public static final String WEB_OFFER_COUPON_REDEMPTION_DETAIL = "WebOfferCouponRedemptionDetail";

	public static final String TRUE_CALLER_PROFILE_DETAIL = "TrueCallerProfileDetail";

	public static final String TRUE_CALLER_PROFILE_DETAIL_SECONDARY = "TrueCallerProfileDetailSecondary";

	public static final String SIGNUP_IMAGE_MAPPING = "SignupImageMapping";

	public static final String LOOKUP_IMAGE_MAPPING = "LookupImageMapping";

	public static final String TRUE_CALLER_REQUEST_DETAIL = "TrueCallerRequestDetail";

	public static final Integer MONK_CALIBRATION_MARGIN_TIME_MS = 1800 * 1000;

	public static final String WHATSAPP = "WHATSAPP";

	public static final List<UnitCategory> categoryList = Arrays.asList(UnitCategory.TAKE_AWAY, UnitCategory.COD,
			UnitCategory.CAFE);

	public static final int PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD = 35;
	public static final int PAYMENT_MODE_PAYTM_EDC_AMEX_CARD = 36;
	public static final int PAYMENT_MODE_PAYTM_EDC_UPI = 37;
	public static final int PAYMENT_MODE_PAYTM_DQR_UPI = 39;
	public static final String KETTLE_ORDER_VERSION = "v2";

	public static final String AMEX_COUPON_CODE = "CHAIAMEX";

	public static final String CLEVERTAP_DATE_PREFIX = "$D_";

	public static final String DEFAULT_TIME_ZONE="Asia/Kolkata";

	public static final String DEFAULT_VARIANT_TYPE="DEFAULT";

	public static final String ORDER_RECEIPT_PATH = "/orders/";

	public static final String CUSTOMER = "CUSTOMER";

	public static final String CRE = "CRE";

	public static final String musicPlaybackRequest = "http://stage.kettle.chaayos.com:8989/m?o=OrderId";
	public static final String ORDER_ID = "OrderId";
	public static final List<Integer> SKIP_LOYALTY_PRODUCTS= Arrays.asList(1377,1000379);

	public static final String PROCESSING="PROCESSING";

	public static final String DECLINE="DECLINE";
	public static final String POS="POS";
	public static final String CAFE_APP="CAFE_APP";

	public static final int UNSATISFIED_CUSTOMER_COMP_CODE = 2102;

	public static final int CASH_SETTLEMENT_MODE = 1;

	public static final List<Integer> ATM_UNSTSFIED_CUST_ELIG_PROD_TYPE = List.of(5,6,7,10,55);

	public static final String GIFT_CARD_PAYMENT_MODE="GiftCard";

	public static final String LOYALTY_OFFER = "LOYALTY_OFFER";

	public static final String OAT_MILK = "OatMilk";

	public static final String oatMilkPrefix = "O_";

	public  static final Integer OAT_MILK_SCM_PRODUCT_ID = 104191;
	public  static final String OAT_MILK_SCM_PRODUCT_NAME = "Oats-Milk";


	public static final String DATE_TIME_STRING = "yyyy-MM-dd HH:mm:ss";

	public static final Integer OAT_MILK_DOHFUL_SCM_PRODUCT_ID = 104371;
	public static final Integer ALMOND_MILK_DOHFUL_SCM_PRODUCT_ID = 104372;

	public  static final String OAT_MILK_DOHFUL_SCM_PRODUCT_NAME = "Oat milk _Dohful";
	public  static final String ALMOND_MILK_DOHFUL_SCM_PRODUCT_NAME = "Almond milk _Dohful";
	public static final Integer DOHFUL_SCM_MILK_PRODUCT_ID = 104349;

	public static final String PRICE_PROFILE_ID = "PRICE_PROFILE_ID";

	public static final String PRICE_PROFILE_VERSION = "PRICE_PROFILE_VERSION";
	public static final String SERVICE_CHARGE = "Optional Service Charge";
	public static final String SERVICE_CHARGE_DESCRIPTION = "We levy an optional service charge which goes to the team involved in serving you today. This is completely optional and at your discretion.";
	public static final Integer SERVICE_CHARGE_SUBTYPE = 4038;

    public static final String SERVICE_CHARGE_REMOVED = "SERVICE_CHARGE_REMOVED";
    public static final String SERVICE_CHARGE_VALUE = "SERVICE_CHARGE_VALUE";
}
