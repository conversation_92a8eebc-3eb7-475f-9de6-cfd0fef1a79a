package com.stpl.tech.kettle.converter;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.master.domain.model.Unit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderItem;
import com.stpl.tech.kettle.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.domain.model.ClevertapChargedEventItemData;
import com.stpl.tech.kettle.domain.model.ClevertapOfferData;
import com.stpl.tech.kettle.domain.model.ClevertapSubscriptionEventData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerProfileCleverTap;
import com.stpl.tech.kettle.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.service.ClevertapAttributesService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapEvents;
import com.stpl.tech.master.domain.model.Product;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
public class CleverTapConverter {

	private static final Long BUFFER_TIME = 20L;

	private static final Long BUFFER_TIME_FOR_NEW_PROFILE = 300L;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private ClevertapAttributesService clevertapAttributesService;

	public CustomerProfileCleverTap convert(Customer customer) {
		log.info("converting data for user profile push in clevertap");
		Map<String, Object> attributeMap = clevertapAttributesService.getProfileAttributes(customer);
		CustomerProfileCleverTap data = new CustomerProfileCleverTap();
		data.setAddTime(AppConstants.CLEVERTAP_DATE_PREFIX + customer.getAddTime().toInstant().getEpochSecond());
		data.setName(customer.getFirstName());
		data.setEmail(customer.getEmailId());
		data.setPhone(customer.getCountryCode() + customer.getContactNumber());
		data.setCustomerId(customer.getId());
		data.setLoyaltyRedeemedCount(((BigDecimal) attributeMap.get("loyaltyRedeemedCount")).toBigInteger().intValue());
		data.setCountryCode(customer.getCountryCode());
		data.setIsNumberVerified(boolToString(customer.isContactNumberVerified()));
		data.setIsEmailVerified(boolToString(customer.isEmailVerified()));
		data.setIsBlacklisted(boolToString(customer.isBlacklisted()));
		data.setIsDnd(boolToString(customer.getIsDND()));
		data.setMsg_sms(boolToString(customer.isSmsSubscriber()));
		data.setOptInSms(customer.isSmsSubscriber());
		data.setMsg_whatsapp(customer.getOptWhatsapp());
		if (Objects.nonNull(customer.getDateOfBirth())) {
			data.setBirthday(AppUtils.getDay(customer.getDateOfBirth()));
			data.setBirthdayMonth(AppUtils.getMonthName(AppUtils.getMonth(customer.getDateOfBirth())));
			data.setDob(AppConstants.CLEVERTAP_DATE_PREFIX + customer.getDateOfBirth().toInstant().getEpochSecond());
		}
		if (Objects.nonNull(customer.getAnniversary())) {
			data.setAnniversary(AppUtils.getDay(customer.getAnniversary()));
			data.setAnniversaryMonth(AppUtils.getMonthName(AppUtils.getMonth(customer.getAnniversary())));
		}
		if (Objects.nonNull(customer.getOrderCount())) {
			data.setOrderCount(customer.getOrderCount());
		} else {
			data.setOrderCount(0);
		}
		data.setAcquisitionSource(customer.getAcquisitionSource());
		if (customer.getAddTime().after(AppUtils.getBusinessDate())
				|| customer.getAddTime().equals(AppUtils.getBusinessDate())) {
			data.setCommunicationName(customer.getFirstName());
		}
		if (Objects.nonNull(data.getLoyaltyPointsBalance())) {
			data.setLoyalteaAvailable(Math.floorDiv(data.getLoyaltyPointsBalance(), 60));
		}
		data.setLastUpdatedTime(AppConstants.CLEVERTAP_DATE_PREFIX + AppUtils.getCurrentTimestamp().getTime());
		if (StringUtils.isNotBlank(customer.getOptWhatsapp())) {
			data.setOptInWhatsapp(customer.getOptWhatsapp().equalsIgnoreCase(AppConstants.YES));
		}
		try {
			data.setTotalSpent((BigDecimal) attributeMap.get("totalSpent"));
		} catch (Exception e) {
			log.info("Casting Exception for totalSpent in Clevertap profile for customer {}", data.getCustomerId());
		}
		try {
			data.setGcBalance((BigDecimal) attributeMap.get("gcBalance"));
		} catch (Exception e) {
			log.info("Casting Exception for gcBalance in Clevertap profile for customer {}", data.getCustomerId());
		}
		try {
			data.setChyCashBalance((BigDecimal) attributeMap.get("chyCashBalance"));
		} catch (Exception e) {
			log.info("Casting Exception for chyCashBalance in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		try {
			data.setLoyaltyPointsBalance((Integer) attributeMap.get("loyaltyPointsBalance"));
		} catch (Exception e) {
			log.info("Casting Exception for loyaltyPointsBalance in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		try {
			data.setLoyaltyRedeemedCount(((BigDecimal) attributeMap.get("loyaltyRedeemedCount")).intValue());
		} catch (Exception e) {
			log.info("Casting Exception for loyaltyRedeemedCount in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		try {
			data.setNboAvailableFlag(((BigInteger) attributeMap.get("nboAvailableFlag")).intValue());
		} catch (Exception e) {
			log.info("Casting Exception for nboAvailableFlag in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		try {
			data.setDnboAvailableFlag(((BigInteger) attributeMap.get("dnboAvailableFlag")).intValue());
		} catch (Exception e) {
			log.info("Casting Exception for dnboAvailableFlag in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		try {
			data.setSubscriptionActiveFlag(((BigInteger) attributeMap.get("subscriptionActiveFlag")).intValue());
		} catch (Exception e) {
			log.info("Casting Exception for subscriptionActiveFlag in Clevertap profile  for customer {}",
					data.getCustomerId());
		}
		if(Objects.nonNull(data.getLoyaltyPointsBalance())){
			data.setFiftyLoyaltyThreshold(data.getLoyaltyPointsBalance() == 50 ? AppConstants.YES : AppConstants.NO);
		}else{
			data.setFiftyLoyaltyThreshold(AppConstants.NO);
		}
		return data;
	}

	public String boolToString(boolean val) {
		return val ? AppConstants.YES : AppConstants.NO;
	}

	public String boolToString(Boolean val) {
		return val.equals(Boolean.TRUE) ? AppConstants.YES : AppConstants.NO;
	}

	public EventUploadRequest convert(OrderDetail order, String evtName, Object data) {
		EventUploadRequest eventRequest = new EventUploadRequest();
		eventRequest.setIdentity(order.getCustomerId());
		eventRequest.setEvtName(evtName);
		try {
			if (data instanceof ClevertapChargedEventData) {
				ClevertapChargedEventData d = (ClevertapChargedEventData) data;
				if (AppConstants.YES.equals(d.getIsNewCustomer())) {
					eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME_FOR_NEW_PROFILE);
				}else {
					eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
				}
			} else {
				eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
			}
		}catch (Exception e){
			log.error("Error in checking for new Customer");
			eventRequest.setTs(order.getBillingServerTime().toInstant().getEpochSecond() + BUFFER_TIME);
		}
		eventRequest.setType(CleverTapConstants.EVENT);
		eventRequest.setEvtData(data);
		return eventRequest;

	}

	public EventUploadRequest convertReceivedChaayosCashData(Integer customerId, String evtName, Object data) {

		EventUploadRequest request = new EventUploadRequest();
		Date currentTime = AppUtils.getCurrentTimestamp();
		request.setTs(currentTime.toInstant().getEpochSecond());
		request.setIdentity(customerId);
		request.setEvtName(evtName);
		request.setType("event");
		request.setEvtData(data);
		return request;
	}

	public ClevertapChargedEventData convertChargedEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		log.info("Inside Converting Charged Event Data ");
		ClevertapChargedEventData orderData = new ClevertapChargedEventData();
		List<ClevertapChargedEventItemData> itemList = new ArrayList<>();
		Map<String, Object> attributeMap = clevertapAttributesService.getEventAttributes(order, customer);
		Map<Integer, Product> productDetails = productCache.getProductDetails();
		calculateAll(order, productDetails, itemList, orderData);
		orderData.setItems(itemList);
		orderData.setUnitId(order.getUnitId());
		Unit unit = unitCacheService.getUnitById(order.getUnitId());
		if(Objects.nonNull(unit)){
			orderData.setUnitCity(unit.getAddress().getCity());
			orderData.setUnitRegion(unit.getRegion());
			orderData.setUnitSubCat(unit.getSubCategory().name());
		}
		orderData.setBrandId(order.getBrandId());
		orderData.setOrderId(order.getOrderId());
		orderData.setOfferCode(order.getOfferCode());
		orderData.setUnitName(unitCacheService.getUnitById(order.getUnitId()).getName());
		orderData.setOrderType(order.getOrderType());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setOrderStatus(order.getOrderStatus());
		orderData.setTotalAmount(order.getTotalAmount());
		orderData.setBusinessDate(
				AppConstants.CLEVERTAP_DATE_PREFIX + AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setDiscountRate(order.getDiscountPercent());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setDiscountAmount(order.getDiscountAmount());
		orderData.setPointsRedeemed(order.getPointsRedeemed() * (-1));
		orderData.setOrderSourceVersion(order.getSourceVersion());
		if (order.getPointsRedeemed() > 0) {
			orderData.setOfferClass("LOYALTEA");
		}
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setBillingServerTime(
				AppConstants.CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		orderData.setDeliveryPartnerId(order.getDeliveryPartnerId());
		try {
			if (Objects.nonNull(attributeMap.get("offerDetailId"))) {
				orderData.setOfferDetailId((Integer) attributeMap.get("offerDetailId"));
			}
		} catch (Exception e) {
			log.info("Casting Exception for offerDetailId in Clevertap charged event  for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousOrderId"))) {
				orderData.setPreviousOrderId((Integer) attributeMap.get("previousOrderId"));
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousOrderId in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousSourceOrderId"))) {
				orderData.setPreviousSourceOrderId((Integer) attributeMap.get("previousSourceOrderId"));
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousSourceOrderId in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousBillingServerTime"))) {
				orderData.setPreviousBillingServerTime(AppConstants.CLEVERTAP_DATE_PREFIX
						+ ((Timestamp) attributeMap.get("previousBillingServerTime")).getTime());
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousBillingServerTime in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousSourceBillingServerTime"))) {
				orderData.setPreviousSourceBillingServerTime(AppConstants.CLEVERTAP_DATE_PREFIX
						+ ((Timestamp) attributeMap.get("previousSourceBillingServerTime")).getTime());
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousSourceBillingServerTime in Clevertap charged event for order {}",
					orderData.getOrderId());
		}
		try {
			orderData.setPreviousBillingServerTime(
					"$D_" + ((Timestamp) attributeMap.get("previousBillingServerTime")).getTime());
		} catch (Exception e) {
			log.info("Casting Exception for previousBillingServerTime in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			orderData.setPreviousSourceBillingServerTime(
					"$D_" + ((Timestamp) attributeMap.get("previousSourceBillingServerTime")).getTime());
		} catch (Exception e) {
			log.info("Casting Exception for previousSourceBillingServerTime in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousOrderTimeDiff"))) {
				orderData.setPreviousOrderTimediff(((BigInteger) attributeMap.get("previousOrderTimeDiff")));
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousOrderTimeDiff in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			if (Objects.nonNull(attributeMap.get("previousSourceOrderTimeDiff"))) {
				orderData
						.setPreviousSourceOrderTimediff(((BigInteger) attributeMap.get("previousSourceOrderTimeDiff")));
			}
		} catch (Exception e) {
			log.info("Casting Exception for previousSourceOrderTimeDiff in Clevertap charged event for order {}",
					orderData.getOrderId());

		}
		try {
			orderData.setOverallOrderCount(new BigInteger(attributeMap.get("overallOrderCount").toString()));
		} catch (Exception e) {
			log.info("Casting Exception for overallOrderCount in Clevertap charged event for order {}",
					orderData.getOrderId());
		}
		try {
			orderData.setOrderSourceOrderCount((BigInteger) attributeMap.get("orderSourceOrderCount"));
		} catch (Exception e) {
			log.info("Casting Exception for orderSourceOrderCount in Clevertap charged event for order {}",
					orderData.getOrderId());
		}
		if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		try {
			orderData.setWalletPendingAmount(((BigDecimal) attributeMap.get("walletPendingAmount")).intValue());
		}catch (Exception e){
			log.info("Casting Exception for walletPendingAmount during BigDecimal conversion in Clevertap charged event for order {} and Error is : {}",
					orderData.getOrderId(),e);
			try{
				orderData.setWalletPendingAmount(((BigInteger) attributeMap.get("walletPendingAmount")).intValue());
			}catch (Exception ex){
				log.info("Casting Exception for walletPendingAmount during BigInteger conversion in Clevertap charged event for order {} and Error is : {}",
						orderData.getOrderId(),e);
			}
		}
		log.info("Charged Event Data Converter : took {} ms", System.currentTimeMillis() - startTime);
		if (Objects.nonNull(orderData.getOverallOrderCount())) {
			orderData.setIsNewCustomer(boolToString((orderData.getOverallOrderCount().compareTo(BigInteger.ONE) <= 0)));
		}
		return orderData;
	}

	private void setChargedEventNotificationPayload(ClevertapChargedEventData orderData,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		try {
			orderData.setSubscriptionName(orderNotification.getSubscriptionName());
			orderData.setOfferDescription(orderNotification.getOfferDescription());
			orderData.setValidDays(orderNotification.getValidDays());
			orderData.setPlanEndDate(orderNotification.getPlanEndDate());
			orderData.setCustomerName(orderNotification.getCustomerName());
			orderData.setSelectOverallSaving(orderNotification.getSelectOverallSaving());
			orderData.setSelectSavingAmount(orderNotification.getSelectSavingAmount());
			orderData.setNextOfferText(orderNotification.getNextOfferText());
			orderData.setValidityTill(orderNotification.getValidityTill());
			orderData.setChannelPartner(orderNotification.getChannelPartner());
			orderData.setLoyalTeaTotalCount(orderNotification.getLoyalTeaTotalCount());
			orderData.setTotalLoyalTeaPoint(orderNotification.getTotalLoyalTeaPoint());
			orderData.setLoyalTeaPoints(orderNotification.getLoyalTeaPoints());
			orderData.setLoyalTeaCount(orderNotification.getLoyalTeaCount());
			orderData.setOrderAmt(orderNotification.getOrderAmt());
			orderData.setSavingAmt(orderNotification.getSavingAmt());
			orderData.setSavingText(orderNotification.getSavingText());
			orderData.setOrderFeedBackUrl(orderNotification.getOrderFeedBackUrl());
			orderData.setOrderRecieptUrl(orderNotification.getOrderRecieptUrl());
			orderData.setIsSubscriptionUsed(orderNotification.getIsSubscriptionUsed());
			orderData.setSubscriptionValidityInDays(orderNotification.getSubscriptionValidityInDays());
			orderData.setWalletPurchaseAmount(Objects.isNull(orderNotification.getWalletPurchaseAmt()) ? 0
					: orderNotification.getWalletPurchaseAmt());
			orderData.setWalletPendingAmount(Objects.isNull(orderNotification.getWalletPendingAmount()) ? 0
					: orderNotification.getWalletPendingAmount());
			orderData.setIsWalletPurchased(orderNotification.getIsWalletPurchased());
			orderData.setWalletSavingAmount(orderNotification.getWalletSavingAmount());
			orderData.setWalletExtraAmount(Objects.isNull(orderNotification.getWalletExtraAmount()) ? BigDecimal.ZERO
					: orderNotification.getWalletExtraAmount());
			orderData.setWalletUsed(orderNotification.getWalletUsed());
			orderData.setGenerateOrderId(orderNotification.getGenerateOrderId());
			orderData.setItemCode(orderNotification.getItemCode());
			orderData.setCashPendingAmount(orderNotification.getCashPendingAmount());
			orderData.setVoucherCode(orderNotification.getVoucherCode());
			orderData.setSmsTemplateDate(orderNotification.getSmsTemplateDate());
			orderData.setUsedAmount(orderNotification.getUsedAmount());
			orderData.setCashBackAmount(orderNotification.getCashBackAmount());
			orderData.setCashBackAllotmentStartDate(orderNotification.getCashBackAllotmentStartDate());
			orderData.setCashBackAllotmentEndDate(orderNotification.getCashBackAllotmentEndDate());
			orderData.setRefundAmount(orderNotification.getRefundAmount());
			orderData.setSmsSubscriber(orderNotification.isSmsSubscriber());
			orderData.setWhatsAppOptIn(orderNotification.isWhatsAppOptIn());
			orderData.setCustomerContactNumber(orderNotification.getCustomerContactNumber());
			orderData.setIsLoyaltyUnlocked(orderNotification.getIsLoyaltyUnlocked());
			orderData.setSubscriptionValidityDaysLeft(orderNotification.getDaysLeft());
			orderData.setIsSubscriptionPurchased(orderNotification.getIsSubscriptionPurched());
		} catch (Exception e) {
			log.error(
					"Exception faced while converting order notification payload to clevertap charged event for orderId :{}",
					orderData.getOrderId(), e);
		}
		log.info("Charged Event Notification Data Converter : took {} ms", System.currentTimeMillis() - startTime);
	}

	private void calculateAll(OrderDetail order, Map<Integer, Product> productDetails,
			List<ClevertapChargedEventItemData> itemList, ClevertapChargedEventData orderData) {
		Map<Integer, Map<String, Object>> tempItemMap = new HashMap<>();
		int beveragePax = 0;
		int foodPax = 0;
		int beverageQuantity = 0;
		int foodQuantity = 0;
		BigDecimal beverageAmount = BigDecimal.ZERO;
		BigDecimal foodAmount = BigDecimal.ZERO;
		int quantity = 0;
		int pax = 0;
		Map<Integer, ClevertapChargedEventItemData> orderItemMap = new HashMap<>();
		for (OrderItem item : order.getOrderItems()) {
			Product product = productDetails.get(item.getProductId());
			checkSubscriptionOrder(product, orderData);
			quantity += item.getQuantity();
			if (Objects.isNull(tempItemMap.get(item.getProductId()))) {
				tempItemMap.put(item.getProductId(), new HashMap<>());
			}
			Map<String, Object> itemAttributes = tempItemMap.get(item.getProductId());
			ClevertapChargedEventItemData currentOrderItem = orderItemMap.get(item.getProductId());
			currentOrderItem = initializeOrderItem(currentOrderItem, itemList, item, product, itemAttributes);
			String foodClass = Objects.nonNull(currentOrderItem) ? currentOrderItem.getFoodClass() : "";
			pax = currentOrderItem.getPaxCount();
			if (foodClass.equalsIgnoreCase("FOOD")) {
				foodPax += pax;
				foodQuantity += item.getQuantity();
				foodAmount = foodAmount.add(item.getPaidAmount());
			} else if (foodClass.equalsIgnoreCase("BEV")) {
				beveragePax += pax;
				beverageQuantity += item.getQuantity();
				beverageAmount = beverageAmount.add(item.getPaidAmount());
			}
			orderItemMap.put(item.getProductId(), currentOrderItem);
		}
		setBeverageAndFoodQuantity(beverageQuantity, foodQuantity, orderData, order);
		orderData.setPax(Math.max(beveragePax, foodPax));
		orderData.setFoodAmount(foodAmount);
		orderData.setBeverageAmount(beverageAmount);
		orderData.setFoodQuantity(foodQuantity);
		orderData.setBeverageQuantity(beverageQuantity);
		orderData.setTotalQuantity(quantity);
	}

	public ClevertapChargedEventData convertWalletEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		log.info("Inside wallet purchase event data converter");
		ClevertapChargedEventData orderData = new ClevertapChargedEventData();
		Map<String, Object> attributeMap = clevertapAttributesService.getEventAttributes(order, customer);
		try {
			Object overallOrderCount = attributeMap.get("overallOrderCount");
			if(overallOrderCount instanceof Long){
				orderData.setIsNewCustomer(boolToString((((Long) overallOrderCount)<= 1 )));
			} else if (overallOrderCount instanceof BigInteger) {
				orderData.setIsNewCustomer(boolToString((((BigInteger) overallOrderCount).compareTo(BigInteger.ONE) <= 0 )));
			}
			else if (overallOrderCount instanceof Integer) {
				orderData.setIsNewCustomer(boolToString((((Integer) overallOrderCount) <= 1 )));
			}} catch (Exception e) {
			log.info("Casting Exception for overallOrderCount in Clevertap charged event for order {}",
					orderData.getOrderId());
		}
		orderData.setUnitId(order.getUnitId());
		orderData.setBrandId(order.getBrandId());
		orderData.setOrderId(order.getOrderId());
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setUnitName(unitCacheService.getUnitById(order.getUnitId()).getName());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setOrderSourceVersion(order.getSourceVersion());
		orderData.setBusinessDate(
				AppConstants.CLEVERTAP_DATE_PREFIX + AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setBillingServerTime(
				AppConstants.CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		log.info("wallet purchase event data converter : took {} ms", System.currentTimeMillis() - startTime);
		return orderData;
	}

	public ClevertapChargedEventData convertSubscriptionEventOrderData(OrderDetail order, CustomerInfo customer,
			OrderNotification orderNotification) {
		long startTime = System.currentTimeMillis();
		log.info("Inside subscription purchase event data converter");
		ClevertapSubscriptionEventData orderData = new ClevertapSubscriptionEventData();
		Map<String, Object> attributeMap = clevertapAttributesService.getSubscriptionAttributes(order, customer);
		orderData.setUnitId(order.getUnitId());
		orderData.setBrandId(order.getBrandId());
		orderData.setChannelPartnerId(order.getChannelPartnerId());
		orderData.setOrderId(order.getOrderId());
		orderData.setUnitName(unitCacheService.getUnitById(order.getUnitId()).getName());
		orderData.setCustomerId(order.getCustomerId());
		orderData.setOrderSource(order.getOrderSource());
		orderData.setOrderStatus(order.getOrderStatus());
		orderData.setTotalAmount(order.getTotalAmount());
		orderData.setOrderSourceVersion(order.getSourceVersion());
		orderData.setBusinessDate(
				AppConstants.CLEVERTAP_DATE_PREFIX + AppUtils.getBusinessDate().toInstant().getEpochSecond());
		orderData.setDiscountRate(order.getDiscountPercent());
		orderData.setTaxableAmount(order.getTaxableAmount());
		orderData.setDiscountAmount(order.getDiscountAmount());
		orderData.setBillingServerTime(
				AppConstants.CLEVERTAP_DATE_PREFIX + order.getBillingServerTime().toInstant().getEpochSecond());
		try {
			Object overallOrderCount = attributeMap.get("overallOrderCount");
			if(overallOrderCount instanceof Long){
				orderData.setIsNewCustomer(boolToString((((Long) overallOrderCount)<= 1 )));
			} else if (overallOrderCount instanceof BigInteger) {
				orderData.setIsNewCustomer(boolToString((((BigInteger) overallOrderCount).compareTo(BigInteger.ONE) <= 0 )));
			}
			else if (overallOrderCount instanceof Integer) {
				orderData.setIsNewCustomer(boolToString((((Integer) overallOrderCount) <= 1 )));
			}} catch (Exception e) {
			log.info("Casting Exception for isNewCustomer in Clevertap charged event for order {}",
					orderData.getOrderId());
		}
		order.getOrderSettlements().forEach(orderSettlement -> {
			if (orderSettlement.getPaymentModeId() == 10)
				orderData.setGcPaymentFlag(1);
		});
		try {
			orderData.setSubscriptionPlanCode((String) attributeMap.get("planCode"));
		} catch (Exception e) {
			log.info("Casting Exception for planCode in Clevertap subscription purchase event for order {}",
					orderData.getOrderId());
		}
		try {
			if (Objects.nonNull(attributeMap.get("planStartDate")))
				orderData.setPlanStartDate(
						AppConstants.CLEVERTAP_DATE_PREFIX + (attributeMap.get("planStartDate")).toString());
		} catch (Exception e) {
			log.info("Casting Exception for planStartDate in Clevertap subscription purchase event for order {}",
					orderData.getOrderId());
		}
		try {
			if (Objects.nonNull(attributeMap.get("planEndDate")))
				orderData.setSubscriptionPlanEndDate(
						AppConstants.CLEVERTAP_DATE_PREFIX + (attributeMap.get("planEndDate")).toString());
		} catch (Exception e) {
			log.info(
					"Casting Exception for subscriptionPlanEndDate in Clevertap subscription purchase event for order {}",
					orderData.getOrderId());
		}
		try {
			orderData.setEventType((String) attributeMap.get("eventType"));
		} catch (Exception e) {
			log.info("Casting Exception for eventType in Clevertap subscription purchase event for order {}",
					orderData.getOrderId());
		}
		if (Objects.nonNull(orderNotification)) {
			setChargedEventNotificationPayload(orderData, orderNotification);
		}
		log.info("subscription purchase event data converter : took {} ms", System.currentTimeMillis() - startTime);
		return orderData;

	}

	private void checkSubscriptionOrder(Product product, ClevertapChargedEventData orderData) {
		if (product.getSubType() == 3810) {
			orderData.setSubscriptionPurchaseFlag(1);
			orderData.setOfferClass("SUBSCRIPTION");
		}
	}

	private ClevertapChargedEventItemData initializeOrderItem(ClevertapChargedEventItemData currentOrderItem,
			List<ClevertapChargedEventItemData> itemList, OrderItem item, Product product,
			Map<String, Object> itemAttributes) {
		String foodClass;
		int previousPax = 0;
		if (Objects.isNull(currentOrderItem)) {
			currentOrderItem = new ClevertapChargedEventItemData();
			itemList.add(currentOrderItem);
			currentOrderItem.setProductId(item.getProductId());
			currentOrderItem.setProductName(item.getProductName());
			currentOrderItem.setItemTotalAmount(item.getTotalAmount());
			currentOrderItem.setItemAmountPaid(item.getPaidAmount());
			currentOrderItem.setQuantity(item.getQuantity());
			currentOrderItem.setProductType(productCache.getProductCategory(product.getType()).getDetail().getName());
			currentOrderItem.setProductSubType(unitCacheService.getSubcategoryById(product.getSubType()).getName());
			int type = product.getType();
			if (type == 5 || type == 6 || type == 54)
				foodClass = "BEV";
			else if (AppConstants.CLEVERTAP_FOOD_PRODUCT_TYPE.contains(type)
					&& !AppConstants.CLEVERTAP_FOOD_PRODUCT_IDS.contains(product.getId())) {
				foodClass = "FOOD";
			} else {
				foodClass = "OTHERS";
			}

		} else {
			currentOrderItem.setItemTotalAmount(item.getTotalAmount().add(currentOrderItem.getItemTotalAmount()));
			currentOrderItem.setItemAmountPaid(item.getPaidAmount().add(currentOrderItem.getItemAmountPaid()));
			currentOrderItem.setQuantity(item.getQuantity() + currentOrderItem.getQuantity());
			foodClass = currentOrderItem.getFoodClass();
			previousPax = currentOrderItem.getPaxCount();
		}
		return calculatePAX(previousPax, item, foodClass, currentOrderItem, itemAttributes);
	}

	private ClevertapChargedEventItemData calculatePAX(int previousPax, OrderItem item, String foodClass,
			ClevertapChargedEventItemData currentOrderItem, Map<String, Object> itemAttributes) {
		String dimension = item.getDimension().trim();
		if (StringUtils.isNotEmpty(dimension)) {
			if (dimension.equalsIgnoreCase("CHOTIKETLI") || dimension.equalsIgnoreCase("FAMILY")) {
				itemAttributes.put("PAX", item.getQuantity() * 2 + previousPax);
				currentOrderItem.setPaxCount(item.getQuantity() * 2 + previousPax);
			} else if (dimension.equalsIgnoreCase("BADIKETLI")) {
				currentOrderItem.setPaxCount(item.getQuantity() * 5 + previousPax);
			} else {
				currentOrderItem.setPaxCount(item.getQuantity() + previousPax);
			}
		} else {
			currentOrderItem.setPaxCount(item.getQuantity() + previousPax);
		}
		currentOrderItem.setFoodClass(foodClass);
		return currentOrderItem;
	}

	private void setBeverageAndFoodQuantity(int beverageQuantity, int foodQuantity, ClevertapChargedEventData orderData,
			OrderDetail order) {
		if (beverageQuantity > 0 && foodQuantity == 0) {
			orderData.setOrderFoodClass("BEV_ONLY");
		} else if (beverageQuantity == 0 && foodQuantity > 0) {
			orderData.setOrderFoodClass("FOOD_ONLY");
		} else if (beverageQuantity > 0 && foodQuantity > 0) {
			orderData.setOrderFoodClass("FOOD_BEV");
		} else {
			orderData.setOrderFoodClass("OTHERS");
		}
		orderData.setOrderStatus(order.getOrderStatus());
		orderData.setGcPurchaseFlag(order.getGiftCardOrder().equalsIgnoreCase("Y") ? 1 : 0);
		orderData.setDiscountAmount(order.getTotalAmount().subtract(order.getTaxableAmount()));
		orderData.setDiscountRate(order.getDiscountPercent());
		order.getOrderSettlements().forEach(orderSettlement -> {
			if (orderSettlement.getPaymentModeId() == 10)
				orderData.setGcPaymentFlag(1);
		});
	}

	public ProfileUploadRequest convert(Integer customerId, long epochSeconds, Object data) {
		ProfileUploadRequest request = new ProfileUploadRequest();
		request.setIdentity(customerId);
		request.setTs(epochSeconds);
		request.setType(CleverTapConstants.PROFILE);
		request.setProfileData(data);
		return request;

	}

	public ClevertapOfferData convert(NextOffer offer, String offerType,
			CustomerCampaignOfferDetail customerCampaignOfferDetail) {
		ClevertapOfferData clevertapOfferData = new ClevertapOfferData();
		clevertapOfferData.setName(customerCampaignOfferDetail.getFirstName());
		clevertapOfferData.setPhone(
				customerCampaignOfferDetail.getCountryCode() + customerCampaignOfferDetail.getContactNumber());

		switch (offerType) {
		case CleverTapEvents.NEXT_BEST_OFFER:
			clevertapOfferData.setNBOCode(offer.getOfferCode());
			clevertapOfferData.setNBOValidityFrom(offer.getValidityFrom());
			clevertapOfferData.setNBOValidityTill(offer.getValidityTill());
			clevertapOfferData.setNBOCreatedAt(AppConstants.CLEVERTAP_DATE_PREFIX
					+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli() / 1000);
			clevertapOfferData.setNBOOfferText(offer.getText());
			break;
		case CleverTapEvents.DELIVERY_NEXT_BEST_OFFER:
			clevertapOfferData.setDNBOCode(offer.getOfferCode());
			clevertapOfferData.setDNBOValidityFrom(offer.getValidityFrom());
			clevertapOfferData.setDNBOValidityTill(offer.getValidityTill());
			clevertapOfferData.setDNBOCreatedAt(AppConstants.CLEVERTAP_DATE_PREFIX
					+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli() / 1000);
			clevertapOfferData.setDNBOOfferText(offer.getText());
			break;
		case CleverTapEvents.GENERAL_OFFER:
			clevertapOfferData.setGenOfferCode(offer.getOfferCode());
			clevertapOfferData.setGenOfferValidityFrom(offer.getValidityFrom());
			clevertapOfferData.setGenOfferValidityTill(offer.getValidityTill());
			clevertapOfferData.setGenCreatedAt(AppConstants.CLEVERTAP_DATE_PREFIX
					+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli() / 1000);
			clevertapOfferData.setGenOfferText(offer.getText());
			break;
		case CleverTapEvents.DELIVERY_GENERAL_OFFER:
			clevertapOfferData.setDeliveryGenCode(offer.getOfferCode());
			clevertapOfferData.setDeliveryGenValidityFrom(offer.getValidityFrom());
			clevertapOfferData.setDeliveryGenValidityTill(offer.getValidityTill());
			clevertapOfferData.setDeliveryGenCreatedAt(AppConstants.CLEVERTAP_DATE_PREFIX
					+ customerCampaignOfferDetail.getCouponGenerationTime().toInstant().toEpochMilli() / 1000);
			clevertapOfferData.setDeliveryGenOfferText(offer.getText());
			break;
		default:
			log.info("unknown offer type for customer {}", offer.getCustomerId());
			break;
		}
		return clevertapOfferData;
	}

}
