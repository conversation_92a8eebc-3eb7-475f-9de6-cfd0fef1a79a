package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.master.domain.model.DreamFolksVoucherDetails;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.ServiceChargeRemovedKey;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.ExternalOrder;

import static com.stpl.tech.kettle.util.Constants.AppConstants.DEFAULT_TIME_ZONE;

@Document(collection = "Order")
@ToString
public class Order extends ExternalOrder implements Serializable {

	private static final long serialVersionUID = -7725662830415594342L;

	@Id
	private String _id;

	@Indexed
	protected Integer orderId;

	protected String generateOrderId;

	protected String externalOrderId;

	protected Integer optionResultEventId;

	protected Integer unitOrderId;

	protected String campaignId;

	protected Integer customerId;

	protected int employeeId;
	protected int pointsRedeemed;
	protected long pointsAcquired;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal cashRedeemed;

	protected String source;

	protected String sourceId;

	protected boolean hasParcel;

	@Indexed
	protected OrderStatus status;

	protected ApplicationName application;
	protected List<OrderItem> orders;
	protected List<EnquiryItem> enquiryItems;

	protected TransactionDetail transactionDetail;
	protected int printCount;

	protected SettlementType settlementType;
	protected List<Settlement> settlements;
	@Indexed
	protected int unitId;

	protected String unitName;

	protected Integer terminalId;

	protected Integer tableNumber;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	protected Date billStartTime;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	protected Date billCreationTime;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date businessDate;
	protected int billCreationSeconds;
	@Indexed
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	protected Date billingServerTime;
	protected int channelPartner;
	protected int deliveryPartner;

	protected Subscription subscriptionDetail;

	protected String offerCode;
	protected List<ActionDetail> reprints;

	protected ActionDetail cancellationDetails;

	protected String orderRemark;

	protected Integer deliveryAddress;

	protected String customerName;
	protected boolean containsSignupOffer;

	protected Boolean employeeMeal;

	protected Integer employeeIdForMeal;

	protected String tempCode;
	protected List<OrderMetadata> metadataList;

	protected boolean newCustomer;

	protected boolean pendingCash;

	protected Integer tokenNumber;

	protected Integer linkedOrderId;

	protected Integer paymentDetailId;

	protected Boolean awardLoyalty;
	protected String orderType;

	protected Integer billBookNo;
	protected boolean ood = false;
	protected Integer tableRequestId;
	protected boolean giftCardOrder;
	protected String qrLink;
	protected String qrHeader;
	protected String orderAttribute;

	protected Integer brandId;
	protected List<OrderDiscountData> orderDiscountData;
	protected String partnerCustomerId;
	protected BigDecimal cashBackAwarded;
	protected Date cashBackStartDate;
	protected Date cashBackEndDate;
	protected Boolean cashBackReceived;
	protected String inAppFeedbackUrl;
	protected Integer rating;
	protected Integer maxRating;
	protected String feedbackType;
	protected Integer earnedLoyaltypoints;
	protected NextOffer nextOffer;
	private OrderInvoice invoice;
	private List<Integer> allowedPaymentIds;
	private Map<String, String> whatsappNotificationPayload = new HashMap<>();
	private String whatsappNotificationPayloadType;
	private Integer refOrderId;
	private boolean subscriptionOrder;
	private BigDecimal cashCardPendingAmt;
	private BigDecimal cashCardExtraAmt;
	private BigDecimal cashCardPrevAmt;
	private String sourceVersion;
	private boolean forceAwardLoyalty;

	private boolean combinedOrder;
	private BigDecimal prepTime;

	private Date lastOrderStatusEventTime;

	private int cashBackLagDays;

	private boolean bypassLoyateaAward;

	private BigDecimal currentWalletAmount;

	private boolean skipLoyaltyProducts;

	private String offerAccountType;

	private Integer noOfPax;

	private String customerType;

	private Boolean loyalteaToBeRedeemed;

	private Boolean autoTokenEnabled = false;

	private OrderItem serviceChargeItem;

	private PriceProfileKey unitPriceProfile;

	private String wastageType;

	private Map<Integer, Integer> nonWastageItemMap;

	private Boolean revalidate = false;

	private String revalidationReason;

	private boolean prioritizedOrder;

	private Integer orderRefundDetailId;

	private ServiceChargeRemovedKey serviceChargeRemovedKey;

	private DreamFolksVoucherDetails dreamFolksVoucherDetails;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer value) {
		this.orderId = value;
	}

	public String getGenerateOrderId() {
		return generateOrderId;
	}

	public void setGenerateOrderId(String value) {
		this.generateOrderId = value;
	}

	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}

	public Integer getUnitOrderId() {
		return unitOrderId;
	}

	public void setUnitOrderId(Integer value) {
		this.unitOrderId = value;
	}

	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String value) {
		this.campaignId = value;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer value) {
		this.customerId = value;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int value) {
		this.employeeId = value;
	}

	public int getPointsRedeemed() {
		return pointsRedeemed;
	}

	public void setPointsRedeemed(int value) {
		this.pointsRedeemed = value;
	}

	public long getPointsAcquired() {
		return pointsAcquired;
	}

	public void setPointsAcquired(long pointsAcquired) {
		this.pointsAcquired = pointsAcquired;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String value) {
		this.source = value;
	}

	public String getSourceId() {
		return sourceId;
	}

	public void setSourceId(String value) {
		this.sourceId = value;
	}

	public boolean isHasParcel() {
		return hasParcel;
	}

	public void setHasParcel(boolean value) {
		this.hasParcel = value;
	}

	public OrderStatus getStatus() {
		return status;
	}

	public void setStatus(OrderStatus value) {
		this.status = value;
	}

	public List<OrderItem> getOrders() {
		if (Objects.isNull(orders)) {
			orders = new ArrayList<OrderItem>();
		}
		return this.orders;
	}

	public List<EnquiryItem> getEnquiryItems() {
		if (Objects.isNull(enquiryItems)) {
			enquiryItems = new ArrayList<EnquiryItem>();
		}
		return this.enquiryItems;
	}

	public TransactionDetail getTransactionDetail() {
		return transactionDetail;
	}

	public void setTransactionDetail(TransactionDetail value) {
		this.transactionDetail = value;
	}

	public int getPrintCount() {
		return printCount;
	}

	public void setPrintCount(int value) {
		this.printCount = value;
	}

	public SettlementType getSettlementType() {
		return settlementType;
	}

	public void setSettlementType(SettlementType value) {
		this.settlementType = value;
	}

	public List<Settlement> getSettlements() {
		if (Objects.isNull(settlements)) {
			settlements = new ArrayList<Settlement>();
		}
		return this.settlements;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int value) {
		this.unitId = value;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String value) {
		this.unitName = value;
	}

	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer value) {
		this.terminalId = value;
	}

	public Integer getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(Integer tableNumber) {
		this.tableNumber = tableNumber;
	}

	public Date getBillStartTime() {
		return billStartTime;
	}

	public void setBillStartTime(Date value) {
		this.billStartTime = value;
	}

	public Date getBillCreationTime() {
		return billCreationTime;
	}

	public void setBillCreationTime(Date value) {
		this.billCreationTime = value;
	}

	public int getBillCreationSeconds() {
		return billCreationSeconds;
	}

	public void setBillCreationSeconds(int value) {
		this.billCreationSeconds = value;
	}

	public Date getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(Date value) {
		this.billingServerTime = value;
	}

	public int getChannelPartner() {
		return channelPartner;
	}

	public void setChannelPartner(int value) {
		this.channelPartner = value;
	}

	public int getDeliveryPartner() {
		return deliveryPartner;
	}

	public void setDeliveryPartner(int value) {
		this.deliveryPartner = value;
	}

	public Subscription getSubscriptionDetail() {
		return subscriptionDetail;
	}

	public void setSubscriptionDetail(Subscription value) {
		this.subscriptionDetail = value;
	}

	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String value) {
		this.offerCode = value;
	}

	public List<ActionDetail> getReprints() {
		if (Objects.isNull(reprints)) {
			reprints = new ArrayList<ActionDetail>();
		}
		return this.reprints;
	}

	public ActionDetail getCancellationDetails() {
		return cancellationDetails;
	}

	public void setCancellationDetails(ActionDetail value) {
		this.cancellationDetails = value;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String value) {
		this.orderRemark = value;
	}

	public Integer getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(Integer value) {
		this.deliveryAddress = value;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String value) {
		this.customerName = value;
	}

	public List<OrderMetadata> getMetadataList() {
		if (Objects.isNull(metadataList)) {
			metadataList = new ArrayList<OrderMetadata>();
		}
		return this.metadataList;
	}

	public void setOrders(List<OrderItem> orders) {
		this.orders = orders;
	}

	public void setEnquiryItems(List<EnquiryItem> enquiryItems) {
		this.enquiryItems = enquiryItems;
	}

	public void setSettlements(List<Settlement> settlements) {
		this.settlements = settlements;
	}

	public void setReprints(List<ActionDetail> reprints) {
		this.reprints = reprints;
	}

	public void setMetadataList(List<OrderMetadata> metadataList) {
		this.metadataList = metadataList;
	}

	public boolean isContainsSignupOffer() {
		return containsSignupOffer;
	}

	public void setContainsSignupOffer(boolean containsSignupOffer) {
		this.containsSignupOffer = containsSignupOffer;
	}

	public Boolean isEmployeeMeal() {
		return employeeMeal;
	}

	public void setEmployeeMeal(Boolean employeeMeal) {
		this.employeeMeal = employeeMeal;
	}

	public Integer getEmployeeIdForMeal() {
		return employeeIdForMeal;
	}

	public void setEmployeeIdForMeal(Integer employeeIdForMeal) {
		this.employeeIdForMeal = employeeIdForMeal;
	}

	public String getTempCode() {
		return tempCode;
	}

	public void setTempCode(String tempCode) {
		this.tempCode = tempCode;
	}

	public boolean isNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(boolean newCustomer) {
		this.newCustomer = newCustomer;
	}

	public boolean isPendingCash() {
		return pendingCash;
	}

	public void setPendingCash(boolean pendingCash) {
		this.pendingCash = pendingCash;
	}

	public ApplicationName getApplication() {
		return application;
	}

	public void setApplication(ApplicationName application) {
		this.application = application;
	}

	public Integer getOptionResultEventId() {
		return optionResultEventId;
	}

	public void setOptionResultEventId(Integer optionResultEventId) {
		this.optionResultEventId = optionResultEventId;
	}

	public Integer getPaymentDetailId() {
		return paymentDetailId;
	}

	public void setPaymentDetailId(Integer paymentDetail) {
		this.paymentDetailId = paymentDetail;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Integer getTokenNumber() {
		return tokenNumber;
	}

	public void setTokenNumber(Integer tokenNumber) {
		this.tokenNumber = tokenNumber;
	}

	public Integer getLinkedOrderId() {
		return linkedOrderId;
	}

	public void setLinkedOrderId(Integer linkedOrderId) {
		this.linkedOrderId = linkedOrderId;
	}

	public Integer getBillBookNo() {
		return billBookNo;
	}

	public void setBillBookNo(Integer billBookNo) {
		this.billBookNo = billBookNo;
	}

	public boolean isOod() {
		return ood;
	}

	public void setOod(boolean ood) {
		this.ood = ood;
	}

	public Boolean getAwardLoyalty() {
		return awardLoyalty;
	}

	public void setAwardLoyalty(Boolean awardLoyalty) {
		this.awardLoyalty = awardLoyalty;
	}

	public Integer getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(Integer tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	public boolean isGiftCardOrder() {
		return giftCardOrder;
	}

	public void setGiftCardOrder(boolean giftCardOrder) {
		this.giftCardOrder = giftCardOrder;
	}

	public BigDecimal getCashRedeemed() {
		return cashRedeemed;
	}

	public void setCashRedeemed(BigDecimal cashRedeemed) {
		this.cashRedeemed = cashRedeemed;
	}

	public String getOrderAttribute() {
		return orderAttribute;
	}

	public void setOrderAttribute(String orderAttribute) {
		this.orderAttribute = orderAttribute;
	}

	public String getQrLink() {
		return qrLink;
	}

	public void setQrLink(String qrLink) {
		this.qrLink = qrLink;
	}

	public String getQrHeader() {
		return qrHeader;
	}

	public void setQrHeader(String qrHeader) {
		this.qrHeader = qrHeader;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<OrderDiscountData> getOrderDiscountData() {
		return orderDiscountData;
	}

	public void setOrderDiscountData(List<OrderDiscountData> orderDiscountData) {
		this.orderDiscountData = orderDiscountData;
	}

	public String getPartnerCustomerId() {
		return partnerCustomerId;
	}

	public void setPartnerCustomerId(String partnerCustomerId) {
		this.partnerCustomerId = partnerCustomerId;
	}

	public BigDecimal getCashBackAwarded() {
		return cashBackAwarded;
	}

	public void setCashBackAwarded(BigDecimal cashBackAwarded) {
		this.cashBackAwarded = cashBackAwarded;
	}

	public Date getCashBackStartDate() {
		return cashBackStartDate;
	}

	public void setCashBackStartDate(Date cashBackStartDate) {
		this.cashBackStartDate = cashBackStartDate;
	}

	public Date getCashBackEndDate() {
		return cashBackEndDate;
	}

	public void setCashBackEndDate(Date cashBackEndDate) {
		this.cashBackEndDate = cashBackEndDate;
	}

	public Boolean getCashBackReceived() {
		return cashBackReceived;
	}

	public void setCashBackReceived(Boolean cashBackReceived) {
		this.cashBackReceived = cashBackReceived;
	}

	public String getInAppFeedbackUrl() {
		return inAppFeedbackUrl;
	}

	public void setInAppFeedbackUrl(String inAppFeedbackUrl) {
		this.inAppFeedbackUrl = inAppFeedbackUrl;
	}

	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	public Integer getMaxRating() {
		return maxRating;
	}

	public void setMaxRating(Integer maxRating) {
		this.maxRating = maxRating;
	}

	public String getFeedbackType() {
		return feedbackType;
	}

	public void setFeedbackType(String feedbackType) {
		this.feedbackType = feedbackType;
	}

	public Integer getEarnedLoyaltypoints() {
		return earnedLoyaltypoints;
	}

	public void setEarnedLoyaltypoints(Integer earnedLoyaltypoints) {
		this.earnedLoyaltypoints = earnedLoyaltypoints;
	}

	public NextOffer getNextOffer() {
		return nextOffer;
	}

	public void setNextOffer(NextOffer nextOffer) {
		this.nextOffer = nextOffer;
	}

	public OrderInvoice getInvoice() {
		return invoice;
	}

	public void setInvoice(OrderInvoice invoice) {
		this.invoice = invoice;
	}

	public List<Integer> getAllowedPaymentIds() {
		return allowedPaymentIds;
	}

	public void setAllowedPaymentIds(List<Integer> allowedPaymentIds) {
		this.allowedPaymentIds = allowedPaymentIds;
	}

	public void setWhatsappNotificationPayload(Map<String, String> whatsappNotificationPayload) {
		this.whatsappNotificationPayload = whatsappNotificationPayload;
	}

	public void setWhatsappNotificationPayloadType(String whatsappNotificationPayloadType) {
		this.whatsappNotificationPayloadType = whatsappNotificationPayloadType;
	}

	public Map<String, String> getWhatsappNotificationPayload() {
		return whatsappNotificationPayload;
	}

	public String getWhatsappNotificationPayloadType() {
		return whatsappNotificationPayloadType;
	}

	public Integer getRefOrderId() {
		return refOrderId;
	}

	public void setRefOrderId(Integer refOrderId) {
		this.refOrderId = refOrderId;
	}

	public Boolean getEmployeeMeal() {
		return employeeMeal;
	}

	public boolean isSubscriptionOrder() {
		return subscriptionOrder;
	}

	public void setSubscriptionOrder(boolean subscriptionOrder) {
		this.subscriptionOrder = subscriptionOrder;
	}

	public BigDecimal getCashCardPendingAmt() {
		return cashCardPendingAmt;
	}

	public void setCashCardPendingAmt(BigDecimal cashCardPendingAmt) {
		this.cashCardPendingAmt = cashCardPendingAmt;
	}

	public BigDecimal getCashCardExtraAmt() {
		return cashCardExtraAmt;
	}

	public void setCashCardExtraAmt(BigDecimal cashCardExtraAmt) {
		this.cashCardExtraAmt = cashCardExtraAmt;
	}

	public BigDecimal getCashCardPrevAmt() {
		return cashCardPrevAmt;
	}

	public void setCashCardPrevAmt(BigDecimal cashCardPrevAmt) {
		this.cashCardPrevAmt = cashCardPrevAmt;
	}

	public String getSourceVersion() {
		return sourceVersion;
	}

	public void setSourceVersion(String sourceVersion) {
		this.sourceVersion = sourceVersion;
	}

	public boolean isForceAwardLoyalty() {
		return forceAwardLoyalty;
	}

	public void setForceAwardLoyalty(boolean forceAwardLoyalty) {
		this.forceAwardLoyalty = forceAwardLoyalty;
	}

	public boolean isCombinedOrder() {
		return combinedOrder;
	}

	public void setCombinedOrder(boolean combinedOrder) {
		this.combinedOrder = combinedOrder;
	}
	
	public BigDecimal getPrepTime() {
		return prepTime;
	}

	public void setPrepTime(BigDecimal prepTime) {
		this.prepTime = prepTime;
	}

	public int getCashBackLagDays() {
		return cashBackLagDays;
	}

	public void setCashBackLagDays(int cashBackLagDays) {
		this.cashBackLagDays = cashBackLagDays;
	}

	public Date getLastOrderStatusEventTime() {
		return lastOrderStatusEventTime;
	}

	public void setLastOrderStatusEventTime(Date lastOrderStatusEventTime) {
		this.lastOrderStatusEventTime = lastOrderStatusEventTime;
	}

	public boolean isBypassLoyateaAward() {
		return bypassLoyateaAward;
	}

	public void setBypassLoyateaAward(boolean bypassLoyateaAward) {
		this.bypassLoyateaAward = bypassLoyateaAward;
	}

	public BigDecimal getCurrentWalletAmount() {
		return currentWalletAmount;
	}

	public void setCurrentWalletAmount(BigDecimal currentWalletAmount) {
		this.currentWalletAmount = currentWalletAmount;
	}

	public boolean isSkipLoyaltyProducts() {
		return skipLoyaltyProducts;
	}

	public void setSkipLoyaltyProducts(boolean skipLoyaltyProducts) {
		this.skipLoyaltyProducts = skipLoyaltyProducts;
	}

	public String getOfferAccountType() {
		return offerAccountType;
	}

	public void setOfferAccountType(String offerAccountType) {
		this.offerAccountType = offerAccountType;
	}

	public Integer getNoOfPax() {
		return noOfPax;
	}

	public void setNoOfPax(Integer noOfPax) {
		this.noOfPax = noOfPax;
	}

	public String getCustomerType() {
		return customerType;
	}

	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}

	public Boolean getLoyalteaToBeRedeemed() {
		return loyalteaToBeRedeemed;
	}

	public void setLoyalteaToBeRedeemed(Boolean loyalteaToBeRedeemed) {
		this.loyalteaToBeRedeemed = loyalteaToBeRedeemed;
	}

	public Boolean getAutoTokenEnabled() {
		return autoTokenEnabled;
	}

	public void setAutoTokenEnabled(Boolean autoTokenEnabled) {
		this.autoTokenEnabled = autoTokenEnabled;
	}

	public OrderItem getServiceChargeItem() {
		return serviceChargeItem;
	}

	public void setServiceChargeItem(OrderItem serviceChargeItem) {
		this.serviceChargeItem = serviceChargeItem;
	}

	public PriceProfileKey getUnitPriceProfile() {
		return unitPriceProfile;
	}

	public void setUnitPriceProfile(PriceProfileKey unitPriceProfile) {
		this.unitPriceProfile = unitPriceProfile;
	}

	public String getWastageType() {
		return wastageType;
	}

	public void setWastageType(String wastageType) {
		this.wastageType = wastageType;
	}

	public Map<Integer, Integer> getNonWastageItemMap() {
		return nonWastageItemMap;
	}

	public void setNonWastageItemMap(Map<Integer, Integer> nonWastageItemMap) {
		this.nonWastageItemMap = nonWastageItemMap;
	}

	public Boolean getRevalidate() { return revalidate; }

	public void setRevalidate(Boolean revalidate) { this.revalidate = revalidate; }

	public String getRevalidationReason() { return revalidationReason; }

	public void setRevalidationReason(String revalidationReason) { this.revalidationReason = revalidationReason; }

	public boolean getPrioritizedOrder() {
		return prioritizedOrder;
	}

	public void setPrioritizedOrder(Boolean prioritizedOrder) {
		this.prioritizedOrder = prioritizedOrder;
	}

	public Integer getOrderRefundDetailId() {
		return orderRefundDetailId;
	}

	public void setOrderRefundDetailId(Integer orderRefundDetailId) {
		this.orderRefundDetailId = orderRefundDetailId;
	}

	public ServiceChargeRemovedKey getServiceChargeRemovedKey() { return this.serviceChargeRemovedKey; }

	public void setServiceChargeRemovedKey(ServiceChargeRemovedKey serviceChargeRemovedKey) { this.serviceChargeRemovedKey = serviceChargeRemovedKey; }

	public void setDreamFolksVoucherDetails(DreamFolksVoucherDetails dreamFolksVoucherDetails) {
		this.dreamFolksVoucherDetails = dreamFolksVoucherDetails;
	}

	public DreamFolksVoucherDetails getDreamFolksVoucherDetails() {
		return dreamFolksVoucherDetails;
	}


}
