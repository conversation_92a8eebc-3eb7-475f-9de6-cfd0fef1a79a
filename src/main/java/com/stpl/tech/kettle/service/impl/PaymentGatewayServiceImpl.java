package com.stpl.tech.kettle.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderExternalSettlementData;
import com.stpl.tech.kettle.data.kettle.OrderPaymentDetail;
import com.stpl.tech.kettle.data.kettle.OrderSettlement;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PaymentStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.repository.kettle.OrderPaymentDetailDao;
import com.stpl.tech.kettle.service.PaymentGatewayService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Log4j2
public class PaymentGatewayServiceImpl implements PaymentGatewayService {

	@Autowired
	private OrderPaymentDetailDao paymentDetailDao;

	@Autowired
	private UnitCacheService unitCacheService;

	@Override
	public OrderPaymentDetail getActivePaymentDetail(String externalOrderId) {
		try {
			return paymentDetailDao.findByExternalOrderIdAndRequestStatus(externalOrderId,
					PaymentStatus.INITIATED.name());
		} catch (Exception e) {
			log.error("Could not find active payment detail for external order id: {}", externalOrderId, e);
		}
		return null;
	}

	@Override
	public void processOrderPayment(Order order, Set<Integer> paymentIdSet, OrderDetail orderDetail, StringBuilder sb) {
		if (Objects.isNull(paymentIdSet)) {
			if (AppUtils.isCafeorTakeAway(order.getChannelPartner()) && TransactionUtils.isRegularOrder(order)
					&& Objects.nonNull(order.getSource()) && (TransactionUtils.isCafeUnit(order.getSource())
							|| TransactionUtils.isTakeawayOrder(order.getSource()))) {
				paymentIdSet = order.getSettlements().stream().map(x -> x.getMode()).collect(Collectors.toSet());
			}
			// Log order object if paymentIdSet is still null after the condition
			if (AppUtils.isCafeorTakeAway(order.getChannelPartner()) && CollectionUtils.isEmpty(paymentIdSet)) {
				log.info("PaymentIdSet is null after condition check. Order details: {}", new Gson().toJson(order));
			}
		}

		if (AppUtils.isDineInOrAppOrder(order.getChannelPartner()) || (Objects.nonNull(paymentIdSet)
				&& (paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD)
						|| paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_AMEX_CARD)
						|| paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_EDC_UPI)
						|| paymentIdSet.contains(AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI)))) {
			for (OrderSettlement settlement : orderDetail.getOrderSettlements()) {

				PaymentMode mode = unitCacheService.getPaymentModeById(settlement.getPaymentModeId());
				if (AppConstants.PAYMENT_MODE_GIFT_CARD != settlement.getPaymentModeId() && (PaymentCategory.ONLINE
						.equals(mode.getCategory())
						|| (settlement.getPaymentModeId() == AppConstants.PAYMENT_MODE_PAYTM_EDC_DEBIT_CREDIT_CARD
						|| settlement.getPaymentModeId() == AppConstants.PAYMENT_MODE_PAYTM_EDC_AMEX_CARD
						|| settlement.getPaymentModeId() == AppConstants.PAYMENT_MODE_PAYTM_EDC_UPI
						|| settlement.getPaymentModeId() == AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI))) {
					if (settlement.getExternalTransactions() != null) {
						for (OrderExternalSettlementData s : settlement.getExternalTransactions()) {
							OrderPaymentDetail paymentDetail = getActivePaymentDetail(s.getExternalTransactionId());
							if (paymentDetail != null) {
								paymentDetail.setOrderId(orderDetail.getOrderId());
								paymentDetail.setOrderSettlementId(settlement.getSettlementId());
								paymentDetailDao.save(paymentDetail);
							}
						}
					}
				}
			}
		} else {
			if (order.getExternalOrderId() != null && order.getExternalOrderId().trim().length() > 0
					&& !isGiftCardPayment(order)) {
				long time = System.currentTimeMillis();
				OrderPaymentDetail paymentDetail = getActivePaymentDetail(order.getExternalOrderId());
				if (paymentDetail != null) {
					paymentDetail.setOrderId(orderDetail.getOrderId());
					paymentDetail.setOrderSettlementId(orderDetail.getOrderSettlements().get(0).getSettlementId());
					paymentDetailDao.save(paymentDetail);
				}
				if (Objects.nonNull(sb)) {
					sb.append("\n########## , STEP I, - , Time Taken To Link The Payment Request----------,"
							+ (System.currentTimeMillis() - time));
				}
			}

		}

		if (order.getPaymentDetailId() != null) {
			Optional<OrderPaymentDetail> data = paymentDetailDao.findById(order.getPaymentDetailId());
			if (data.isPresent()) {
				OrderPaymentDetail orderPaymentDetail = data.get();
				orderPaymentDetail.setOrderId(orderDetail.getOrderId());
				orderPaymentDetail.setOrderSettlementId(orderDetail.getOrderSettlements().get(0).getSettlementId());
				orderPaymentDetail.setPaymentStatus(PaymentStatus.SUCCESSFUL.name());
				orderPaymentDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
				paymentDetailDao.save(orderPaymentDetail);
			}
		}

	}

	@Override
	public boolean isGiftCardPayment(Order order) {
		boolean gcPayment = false;
		for (Settlement s : order.getSettlements()) {
			if (s.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
				gcPayment = true;
			}
		}
		return gcPayment;
	}

}
