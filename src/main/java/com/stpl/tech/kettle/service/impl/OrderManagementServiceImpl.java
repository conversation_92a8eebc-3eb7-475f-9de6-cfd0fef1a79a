package com.stpl.tech.kettle.service.impl;

import com.amazon.sqs.javamessaging.SQSSession;
import com.google.gson.Gson;
import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.DreamFolksVoucherUsageCache;
import com.stpl.tech.kettle.cache.EnvironmentPropertiesCache;
import com.stpl.tech.kettle.cache.MappingCache;
import com.stpl.tech.kettle.cache.OfferUsageCache;
import com.stpl.tech.kettle.cache.OrderInfoCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.RecipeCache;
import com.stpl.tech.kettle.cache.StateTransitionCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.cache.UnitSessionCache;
import com.stpl.tech.kettle.converter.DeliveryDetailConverter;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.converter.OrderMetricConverter;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CashCardCorrectionLog;
import com.stpl.tech.kettle.data.kettle.CashCardDetail;
import com.stpl.tech.kettle.data.kettle.CashCardEvent;
import com.stpl.tech.kettle.data.kettle.CashCardEventsLogData;
import com.stpl.tech.kettle.data.kettle.CashCardNotificationData;
import com.stpl.tech.kettle.data.kettle.CashCardOffer;
import com.stpl.tech.kettle.data.kettle.CustomerAddressInfo;
import com.stpl.tech.kettle.data.kettle.CustomerBrandMapping;
import com.stpl.tech.kettle.data.kettle.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.DeliveryDetail;
import com.stpl.tech.kettle.data.kettle.DreamfolksTransactionDetail;
import com.stpl.tech.kettle.data.kettle.EmployeeMealAllowanceData;
import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import com.stpl.tech.kettle.data.kettle.LoyaltyEvents;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderExternalSettlementData;
import com.stpl.tech.kettle.data.kettle.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemAddon;
import com.stpl.tech.kettle.data.kettle.OrderItemInvoice;
import com.stpl.tech.kettle.data.kettle.OrderItemInvoiceTaxDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemTaxDetail;
import com.stpl.tech.kettle.data.kettle.OrderMetricDetailData;
import com.stpl.tech.kettle.data.kettle.OrderPaymentDenominationDetail;
import com.stpl.tech.kettle.data.kettle.OrderRefundDetail;
import com.stpl.tech.kettle.data.kettle.OrderSettlement;
import com.stpl.tech.kettle.data.kettle.OrderStatusEvent;
import com.stpl.tech.kettle.data.kettle.OrderTaxDetail;
import com.stpl.tech.kettle.data.kettle.PartnerOrderDiscountMapping;
import com.stpl.tech.kettle.data.kettle.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.data.kettle.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.kettle.UnitTableMappingDetail;
import com.stpl.tech.kettle.data.kettle.WebOrderMetricDetailData;
import com.stpl.tech.kettle.domain.enums.SuperUEventStatus;
import com.stpl.tech.kettle.domain.model.CashCardEventStatus;
import com.stpl.tech.kettle.domain.model.CashCardStatus;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.ComboQunantityStrategy;
import com.stpl.tech.kettle.domain.model.CreateOrderResult;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerSMSNotificationType;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.LastNOrderResponse;
import com.stpl.tech.kettle.domain.model.LoyaltyEventType;
import com.stpl.tech.kettle.domain.model.LoyaltyFailedReason;
import com.stpl.tech.kettle.domain.model.NotificationPayload;
import com.stpl.tech.kettle.domain.model.NotificationType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDiscountData;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.OrderEmailEntryType;
import com.stpl.tech.kettle.domain.model.OrderFetchStrategy;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderMappingCache;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.domain.model.OrderMetricDomain;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderUnitMapping;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.StateTransitionObject;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionProduct;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;
import com.stpl.tech.kettle.domain.model.SuperUEvent;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableSettlement;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.TableViewOrder;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.TransactionType;
import com.stpl.tech.kettle.domain.model.TransitionData;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.domain.model.UnitSessionDetail;
import com.stpl.tech.kettle.domain.model.UnitTerminalDetail;
import com.stpl.tech.kettle.domain.model.WalletOrder;
import com.stpl.tech.kettle.domain.model.WalletOrderType;
import com.stpl.tech.kettle.domain.model.WebOrderMetricDomain;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.mapper.PartnerOrderRiderStatesDetailMapper;
import com.stpl.tech.kettle.mapper.SubscriptionPlanMapper;
import com.stpl.tech.kettle.notification.AttachmentData;
import com.stpl.tech.kettle.notification.OfferRevalidationFailedTemplate;
import com.stpl.tech.kettle.notification.OrderPushNotification;
import com.stpl.tech.kettle.notification.OrderRevalidationFailedNotification;
import com.stpl.tech.kettle.notification.receipt.QRRawPrintReceipt;
import com.stpl.tech.kettle.publisher.SuperUEventPublisher;
import com.stpl.tech.kettle.repository.kettle.CashCardCorrectionLogDao;
import com.stpl.tech.kettle.repository.kettle.CashCardDetailDao;
import com.stpl.tech.kettle.repository.kettle.CashCardEventDao;
import com.stpl.tech.kettle.repository.kettle.CashCardEventsLogDataDao;
import com.stpl.tech.kettle.repository.kettle.CashCardOfferDao;
import com.stpl.tech.kettle.repository.kettle.CustomerAddressInfoDao;
import com.stpl.tech.kettle.repository.kettle.CustomerBrandMappingDao;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.kettle.CustomerFavChaiMappingDao;
import com.stpl.tech.kettle.repository.kettle.DeliveryDetailDao;
import com.stpl.tech.kettle.repository.kettle.DreamfolksTransactionDetailDao;
import com.stpl.tech.kettle.repository.kettle.EmployeeMealAllowanceDataDao;
import com.stpl.tech.kettle.repository.kettle.EmployeeMealDataDao;
import com.stpl.tech.kettle.repository.kettle.LoyaltyEventsDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderExternalSettlementDataDao;
import com.stpl.tech.kettle.repository.kettle.OrderInvoiceDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemAddonDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemInvoiceDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemInvoiceTaxDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemMetaDataDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemTaxDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderMetricDetailDataDao;
import com.stpl.tech.kettle.repository.kettle.OrderPaymentDenominationDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderRefundDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderSettlementDao;
import com.stpl.tech.kettle.repository.kettle.OrderStatusEventDao;
import com.stpl.tech.kettle.repository.kettle.OrderTaxDetailDao;
import com.stpl.tech.kettle.repository.kettle.PartnerOrderDetailDao;
import com.stpl.tech.kettle.repository.kettle.PartnerOrderDiscountMappingDao;
import com.stpl.tech.kettle.repository.kettle.PartnerOrderRiderStatesDetailDao;
import com.stpl.tech.kettle.repository.kettle.StateSequenceIdDao;
import com.stpl.tech.kettle.repository.kettle.SubscriptionDetailDao;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanDao;
import com.stpl.tech.kettle.repository.kettle.UnitSequenceIdDao;
import com.stpl.tech.kettle.repository.kettle.UnitTableMappingDetailDao;
import com.stpl.tech.kettle.repository.kettle.UnitTokenSequenceDao;
import com.stpl.tech.kettle.repository.kettle.WebOrderMetricDetailDataDao;
import com.stpl.tech.kettle.service.AsyncExecutorService;
import com.stpl.tech.kettle.service.CardService;
import com.stpl.tech.kettle.service.CashBackService;
import com.stpl.tech.kettle.service.CashPacketService;
import com.stpl.tech.kettle.service.CouponService;
import com.stpl.tech.kettle.service.CustomerCommunicationEventPublisher;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.service.DeliveryRequestService;
import com.stpl.tech.kettle.service.EmployeeMealService;
import com.stpl.tech.kettle.service.FeedbackManagementService;
import com.stpl.tech.kettle.service.FirebaseNotificationService;
import com.stpl.tech.kettle.service.FreeItemOfferService;
import com.stpl.tech.kettle.service.InventoryService;
import com.stpl.tech.kettle.service.LoyaltyService;
import com.stpl.tech.kettle.service.NotificationService;
import com.stpl.tech.kettle.service.OfferManagementService;
import com.stpl.tech.kettle.service.OrderEnquiryItemService;
import com.stpl.tech.kettle.service.OrderManagementService;
import com.stpl.tech.kettle.service.OrderMetadataService;
import com.stpl.tech.kettle.service.OrderService;
import com.stpl.tech.kettle.service.PaymentGatewayService;
import com.stpl.tech.kettle.service.RulesEventService;
import com.stpl.tech.kettle.service.SCMService;
import com.stpl.tech.kettle.service.SMSClientProviderService;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.service.SolsInfiniWebServiceClient;
import com.stpl.tech.kettle.service.SubscriptionPlanService;
import com.stpl.tech.kettle.service.TableOrderManagementService;
import com.stpl.tech.kettle.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.service.VoucherManagementService;
import com.stpl.tech.kettle.service.VoucherService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CashCardSMSNotificationType;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.kettle.util.KeyMutexFactory;
import com.stpl.tech.kettle.util.RevalidateHelper;
import com.stpl.tech.kettle.util.RevalidationReason;
import com.stpl.tech.kettle.util.TaxationDetailDao;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.consumptionHelper.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.util.consumptionHelper.ItemConsumptionHelper;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.DreamFolksVoucherDetails;
import com.stpl.tech.master.domain.model.FrequencyOfferType;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.ProductInventory;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.ProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.PrintType;
import jakarta.persistence.NoResultException;
import lombok.extern.log4j.Log4j2;
import net.bull.javamelody.internal.common.LOG;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.util.Pair;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.SerializationUtils;

import javax.jms.JMSException;
import javax.persistence.Query;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Log4j2
public class OrderManagementServiceImpl implements OrderManagementService {

    private static final String LOYALTY_EVENT_SUCCESS_STATUS = "SUCCESS";

    @Autowired
    private VoucherService voucherService;

    @Autowired
    private UnitCacheService unitCacheService;

    @Autowired
    private CashBackService cashBackService;

    @Autowired
    private UnitInventoryManagementService inventoryService;

    @Autowired
    private FreeItemOfferService freeItemOfferService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private FeedbackManagementService feedbackManagementService;

    @Autowired
    private OrderConverter orderConverter;

    @Autowired
    private SubscriptionPlanDao subscriptionPlanDao;

    @Autowired
    private CustomerAddressInfoDao customerAddressInfoDao;

    @Autowired
    private OrderInvoiceDetailDao orderInvoiceDetailDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private BrandMetaDataCache brandMetaDataCache;

    @Autowired
    private LoyaltyEventsDao loyaltyEventsDao;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private DeliveryDetailConverter deliveryDetailConverter;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private OfferUsageCache offerUserCache;

    @Autowired
    private RevalidationServiceImpl revalidationService;

    @Autowired
    private OrderRefundDetailDao orderRefundDetailDao;

    @Autowired
    private OrderItemMetaDataDetailDao orderItemMetaDataDetailDao;

    @Autowired
    private PartnerOrderRiderStatesDetailDao partnerOrderRiderStatesDetailDao;

    @Autowired
    private PartnerOrderDetailDao partnerOrderDetailDao;

    @Autowired
    private PartnerOrderRiderStatesDetailMapper partnerOrderRiderStatesDetailMapper;

    @Autowired
    private OfferManagementService offerManagementService;

    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    private DeliveryRequestService deliveryRequestService;

    @Autowired
    private KeyMutexFactory keyMutexFactory;

    @Autowired
    private OrderMappingCache orderMappingCache;

    @Autowired
    private UnitSessionCache unitSessionCache;

    @Autowired
    private OrderEnquiryItemService enquiryItemService;

    @Autowired
    private UnitSequenceIdDao sequenceIdDao;

    @Autowired
    private UnitTokenSequenceDao tokenSequenceDao;

    @Autowired
    private SubscriptionDetailDao subscriptionDetailDao;

    @Autowired
    private OrderItemDao orderItemDao;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private CashCardOfferDao cashCardOfferDao;

    @Autowired
    private CashCardDetailDao cashCardDetailDao;

    @Autowired
    private VoucherManagementService voucherManagementService;

    @Autowired
    private CashCardEventsLogDataDao cardEventsLogDataDao;

    @Autowired
    private OrderItemAddonDao orderItemAddonDao;

    @Autowired
    private OrderTaxDetailDao orderTaxDetailDao;

    @Autowired
    private OrderItemTaxDetailDao itemTaxDetailDao;

    @Autowired
    private StateSequenceIdDao stateSequenceIdDao;

    @Autowired
    private OrderItemInvoiceDao orderItemInvoiceDao;

    @Autowired
    private OrderItemInvoiceTaxDetailDao itemInvoiceTaxDetailDao;

    @Autowired
    private OrderSettlementDao orderSettlementDao;

    @Autowired
    private OrderExternalSettlementDataDao settlementDataDao;

    @Autowired
    private OrderPaymentDenominationDetailDao paymentDenominationDetailDao;

    @Autowired
    private CashCardEventDao cashCardEventDao;

    @Autowired
    private CashCardCorrectionLogDao cashCardCorrectionLogDao;

    @Autowired
    private OrderStatusEventDao orderStatusEventDao;

    @Autowired
    private EmployeeMealDataDao employeeMealDataDao;

    @Autowired
    private EmployeeMealAllowanceDataDao mealAllowanceDataDao;

    @Autowired
    private PartnerOrderDiscountMappingDao discountMappingDao;

    @Autowired
    private OrderStatusEventDao statusEventDao;

    @Autowired
    private LoyaltyService loyaltyService;

    @Autowired
    private EnvironmentPropertiesCache propertiesCache;

    @Autowired
    public PaymentGatewayService paymentGatewayService;

    @Autowired
    private RulesEventService rulesEventService;

    @Autowired
    private TableOrderManagementService tableDataService;

    @Autowired
    private CashPacketService cashPacketService;

    @Autowired
    private SubscriptionPlanService subscriptionPlanService;

    @Autowired
    private OrderMetadataService orderMetadataService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SCMService scmService;

    @Autowired
    private EmployeeMealService employeeMealService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private ProductCache productCache;

    @Autowired
    private CardService cardService;

    @Autowired
    private SMSClientProviderService providerService;

    @Autowired
    private InventoryService service;

    @Autowired
    private ItemConsumptionHelper itemConsumptionHelper;

    @Autowired
    private OrderInfoCache orderInfoCache;

	@Autowired
	private SimpMessagingTemplate template;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @Autowired
    private FirebaseNotificationService firebaseNotificationService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    private SQSSession session;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    private MappingCache mappingCache;

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Autowired
    private OrderMetricDetailDataDao orderMetricDao;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CustomerBrandMappingDao customerBrandMappingDao;

    @Autowired
    private CustomerFavChaiMappingDao favChaiMappingDao;

    @Autowired
    private UnitTableMappingDetailDao tableMappingDetailDao;

    @Autowired
    private WebOrderMetricDetailDataDao webOrderMetricDao;

    @Autowired
    private SuperUEventPublisher superUEventPublisher;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    @Autowired
    private DreamfolksTransactionDetailDao dreamfolksTransactionDetailDao;

    @Autowired
    private DreamFolksVoucherUsageCache dreamFolksVoucherUsageCache;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo processPlainOrder(Order order, boolean includeReceipts, boolean addMetadata,
                                       OrderNotification orderNotification, String randomString, OrderUnitMapping orderUnitMapping)
            throws CardValidationException, DataNotFoundException, DataUpdationException, TemplateRenderingException,
            JMSException {

        BigDecimal deductedAmount = processCashCardPayment(order);
        BigDecimal prevAmt = order.getCashCardPrevAmt();
        if (Objects.isNull(orderNotification)) {
            orderNotification = new OrderNotification();
        }

        StringBuilder sb = new StringBuilder();
        long orderStartTime = System.currentTimeMillis();

        /**
         * Pre-Order-Creation Processing
         **/
        order = preOrderCreation(order, sb);

        boolean flag = convertToKettleOrderItem(order.getOrders());

        if (flag){
            try {
                log.info("Null Variant Exception :::: {}", (new JSONObject(order)).toString());
            }catch (Exception e){

            }
        }

        CreateOrderResult result = placeOrder(order, sb);
        /**
         * Post-Order-Creation Processing
         **/
        OrderInfo info;
        if(TransactionUtils.isTableOrder(order)){
             info = postTableOrderCreation(order, result, includeReceipts, addMetadata, orderNotification, sb,
                    randomString, orderUnitMapping);
        }else{
             info = postOrderCreation(order, result, includeReceipts, addMetadata, orderNotification, sb,
                    randomString, orderUnitMapping);
        }
        info.setCashCardDeductedAmt(deductedAmount);
//		sb.append(String.format(
//				"\n----------- ,,,Full Order Processing Time, ---------------------------- ,%d, milliseconds ",
//				System.currentTimeMillis() - orderStartTime));
        //info.setSubscriptionPlan(subscriptionPlanMapper.toDomain(result.getSubscriptionPlan()));
        log.info(sb.toString());
        info.setLoyaltyAwarded(Objects.nonNull(info.getOrder().getEarnedLoyaltypoints())
                && info.getOrder().getEarnedLoyaltypoints() > 0);
        info.setCashCardPrevAmt(prevAmt);
        generateOrderNotifcationEvent(info);
        addDreamFolksTransactionDetail(info,order.getDreamFolksVoucherDetails());
        return info;
    }

    @Override
    public void pushDataToThirdPartyAnalytics(OrderInfo info, Map<Integer, OrderNotification> orderNotificationMap) {
        for (Integer orderId : orderNotificationMap.keySet()) {
            asyncExecutorService.deleteReciepts(info.getUnit().getId(), orderId);
        }
        asyncExecutorService.pushDataToThirdPartyAnalytics(info, false, orderNotificationMap);
    }

    @Override
    public void pushDataToCleverTapForPartner(OrderInfo info, Map<Integer, OrderNotification> orderNotificationMap){
        for (Integer orderId : orderNotificationMap.keySet()) {
            asyncExecutorService.deleteReciepts(info.getUnit().getId(), orderId);
        }
        asyncExecutorService.pushDataToCleverTapForPartner(info, false, orderNotificationMap);
    }

    @Override
    public List<LastNOrderResponse> getLastNOrders(Integer size, Integer unitId) {
        if (Objects.isNull(size)) {
            size = 6;
        }
        if (Objects.isNull(unitId)) {
            log.info("Unit id is not given");
        }
        try {
            List<OrderDetail> orderDetails = orderDetailDao.findLastNGeneratedOrderIds(unitId, size);
            List<LastNOrderResponse> lastNOrderResponses = new ArrayList<>();
            for (OrderDetail orderDetail : orderDetails) {
                lastNOrderResponses.add(LastNOrderResponse.builder().generatedOrderId(orderDetail.getGeneratedOrderId())
                        .customerName(orderDetail.getCustomerName()).build());
            }
            return lastNOrderResponses;
        } catch (Exception e) {
            log.error("error while fetching ");
        }
        return new ArrayList<>();
    }

    private boolean convertToKettleOrderItem(List<OrderItem> orderItems) {
        boolean flag = false;
        for (OrderItem orderItem : orderItems) {
            //TODO Remove set tax zero for loyaltea when cafe app rollout is done.
            if (Objects.nonNull(orderItem.getComplimentaryDetail()) && orderItem.getComplimentaryDetail().isIsComplimentary() && orderItem.getComplimentaryDetail().getReasonCode().equals(AppConstants.COMPLEMENTARY_CODE_LOYALTY)) {
                orderItem.setTax(BigDecimal.ZERO);
                if (!CollectionUtils.isEmpty(orderItem.getTaxes())) {
                    for (TaxDetail taxDetail : orderItem.getTaxes()) {
                        taxDetail.setTaxable(BigDecimal.ZERO);
                        taxDetail.setValue(BigDecimal.ZERO);
                    }
                }
            }

            if (Objects.nonNull(orderItem.getComposition())) {
                RecipeDetail recipeDetail = recipeCache.getRecipeByRecipeId(orderItem.getRecipeId());
                if (!CollectionUtils.isEmpty(orderItem.getComposition().getAddons())) {
                    for (IngredientProductDetail detail : orderItem.getComposition().getAddons()) {
                        Product product = productCache.getProductById((detail.getId() == 0)? detail.getProduct().getProductId() :detail.getId());
                        ProductData data = ProductData.builder().productId(product.getId()).name(product.getName())
                                .displayName(product.getName()).shortCode(null).type(product.getType())
                                .subType(product.getSubType()).variantLevelOrdering(false)
                                .classification(product.getClassification()).build();
                        detail.setProduct(data);
                    }
                }
                if (!CollectionUtils.isEmpty(orderItem.getComposition().getVariants())) {
                    for (IngredientVariantDetail detail : orderItem.getComposition().getVariants()) {
                        if (StringUtils.isBlank(detail.getAlias())) {
                            if (StringUtils.isNotBlank(detail.getName())) {
                                detail.setAlias(detail.getName());
                            } else {
                                log.info("Name or Alias not found for variant id {}", detail.getId());
                            }
                        }
                        if (Objects.isNull(detail.getQuantity()) || Objects.isNull(detail.getUom())) {
                          flag =  handleNullAddonsAndVariants(detail, recipeDetail);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(orderItem.getComposition().getAddons())) {
                    for (IngredientProductDetail detail : orderItem.getComposition().getAddons()) {
                        if (Objects.isNull(detail.getQuantity()) || Objects.isNull(detail.getUom())
                                || Objects.isNull(detail.getDimension())) {
                            handleNullAddonsAndVariants(detail, recipeDetail);
                        }
                    }

                }
                if (!CollectionUtils.isEmpty(orderItem.getComposition().getMenuProducts())) {
                    convertToKettleOrderItem(orderItem.getComposition().getMenuProducts());
                }
            }
        }
        return flag;
    }

    private Order preOrderCreation(Order order, StringBuilder sb)
            throws CardValidationException, DataNotFoundException, DataUpdationException {
        Unit unitData = unitCacheService.getUnitById((order.getUnitId()));
        order.setWhatsappNotificationPayloadType(AppConstants.ORDER_TYPE_REGULAR.toUpperCase());
        long startTime = System.currentTimeMillis();
        boolean newCustomer = order.isNewCustomer();

        if(Boolean.TRUE.equals(unitData.getTestingUnit())) {
            order.setOrderType(AppConstants.ORDER_TYPE_TESTING_ORDER);
        } else if (StringUtils.isBlank(order.getOrderType())) {
            order.setOrderType(AppConstants.ORDER_TYPE_REGULAR);
        }
        // Check Wastage
        if (TransactionUtils.isSpecialOrder(order) && !TransactionUtils.isEmployeeMeal(order)) {
            scmService.checkBookWastage(order);
        }
        voucherService.verifyVoucher(order, false);
        if (!TransactionUtils.isCODOrder(order.getSource())) {
            employeeMealService.validateEmployeeMeal(order);
        }

        employeeMealService.validatePaidEmployeeMeal(order);

        complimentryValidation(order);

        // [HACKED] Difficult to set it up at the UI level. Needs to move to UI
        couponService.applyLoyaltyCode(order);// TODO change to validation only

        // [HACKED] need to make dynamic via apply coupon check
        couponService.checkAmex(order);

        cashBackService.checkCashBack(order);

        cashBackService.awardCashBackOffer(order);

        sb.append(String.format(
                "\n----------- ,STEP 1, - ,Employee Meal and Complimentry Validation, ----------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        validateGiftCardAndSubscription(order);
        // SETTING order status before creating database entry
        if (isRoutedToAssembly(unitData, order, order.isGiftCardOrder(), order.isSubscriptionOrder())) {
            order.setStatus(OrderStatus.CREATED);
        } else {
            order.setStatus(OrderStatus.SETTLED);
        }

        if (order.getOrders().get(0).getProductId() != 3) {
            order = applyFreeItemOffer(newCustomer, order);
        }

        return order;
    }



    private OrderInfo postOrderCreation(Order order, CreateOrderResult result, boolean includeReceipts,
                                        boolean addMetadata, OrderNotification orderNotification, StringBuilder sb,
                                        String randonOrderGeneratedString, OrderUnitMapping orderUnitMapping)
            throws DataNotFoundException, TemplateRenderingException, JMSException {
        long startTime = System.currentTimeMillis();
        boolean newCustomer = order.isNewCustomer();

        OrderInfo info = generateAndSaveMetadataPostOrderCreation(order, result, includeReceipts, newCustomer, sb);

        updateMetadataPostOrderCreation(order, result, info, sb);

        sendNotificationsPostOrderCreation(unitCacheService.getUnitById(order.getUnitId()), order, result, info,
                orderNotification, sb);

        updateAndPublishInventory(order, result, info, orderNotification, sb);

        startTime = System.currentTimeMillis();
        if (TransactionUtils.isSpecialOrder(info.getOrder()) && !TransactionUtils.isEmployeeMeal(info.getOrder())) {
            scmService.bookWastage(info.getOrder());
        }
        sb.append(String.format("\n----------- ,STEP 12, - ,Book Wastage, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // 13
        startTime = System.currentTimeMillis();
        // allot cashback
        allotCashBack(order, result, info, orderNotification);
        sb.append(String.format("\n----------- ,STEP 13, - ,Alot Cashback, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        // TODO Allot NBO and DNBO
        offerManagementService.createNBOandDNBOOffers(order, result, info, sb);

        if (addMetadata) {
            info.getOrder().setMetadataList(order.getMetadataList());
        }

        if (!properties.getSystemGeneratedNotifications()) {
            // set order related metadata in orderNotification like unitName , customerName
            // etc
            getOrderMetadataNotificationPayload(info, orderNotification);
        }

        if (Objects.nonNull(order.getPointsAcquired())) {
            log.info("Checking if acquired points are set in order for orderId ::{} and points are::{} ",
                    result.getOrderId(), order.getPointsAcquired());
        }

        orderMappingCache.setGeneratedOrderId(orderUnitMapping, randonOrderGeneratedString,
                info.getOrder().getGenerateOrderId());
        /*
         * if (Objects.nonNull(order.getPointsAcquired()) && order.getPointsAcquired() >
         * 0 && Objects.nonNull(info.getCustomer().getOptWhatsapp()) &&
         * info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)){
         * sendLoyalTeaWhatsappNotification(order,info,orderNotification); }
         */
        boolean hasSelectOrder = hasSelectOrder(order);

        sendLoyalTeaNotifications(order, info, orderNotification, hasSelectOrder);

        info.setOrderNotification(orderNotification);
        saveOrderCountOfSavedChai(order,info.getOrder().getOrderId());
        // log.info("Printing order Notification Payload for orderId ::{}
        // :::{}",order.getOrderId(),new Gson().toJson(orderNotification));
        if(AppConstants.YES.equalsIgnoreCase(info.getUnit().getIsSuperUEnabled())){
            SuperUEvent superUEvent =  superUEventPublisher.getSuperUEvent(info.getOrder());
            if(superUEvent!=null){
               boolean isPushed = superUEventPublisher.pushSuperUEvent(superUEvent,properties.getEnvironmentType().name());
                if(!isPushed){
                    superUEventPublisher.updateEventTrackStatus(SuperUEventStatus.FAILED_TO_PUSH,superUEvent.getEventId());
                }
            }
        }
        return info;
    }


    private OrderInfo postTableOrderCreation(Order order, CreateOrderResult result, boolean includeReceipts,
                                        boolean addMetadata, OrderNotification orderNotification, StringBuilder sb,
                                        String randonOrderGeneratedString, OrderUnitMapping orderUnitMapping)
            throws DataNotFoundException, TemplateRenderingException, JMSException {
        long startTime = System.currentTimeMillis();
        boolean newCustomer = order.isNewCustomer();

       //OrderInfo info = generateAndSaveMetadataPostOrderCreation(order, result, includeReceipts, newCustomer, sb);
        startTime = System.currentTimeMillis();
        OrderInfo info = createOrderInfo(result.getOrderId(), order, includeReceipts,false , null, result,
                newCustomer);
        for(OrderItem oi : info.getOrder().getOrders()){
            if(result.getOrderItemIdsToBeOnHold().contains(oi.getItemId())) {
                oi.setIsHoldOn(AppConstants.YES);
            }
            if(Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())){
                for(OrderItem mi : oi.getComposition().getMenuProducts()){
                    if(result.getOrderItemIdsToBeOnHold().contains(mi.getItemId())) {
                        mi.setIsHoldOn(AppConstants.YES);
                    }
                }
            }
        }
        sb.append(String.format(
                "\n----------- ,STEP 4, - ,Created Order Info, --------------------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        updateAndPublishInventory(order, result, info, orderNotification, sb);

        startTime = System.currentTimeMillis();
        if (TransactionUtils.isSpecialOrder(info.getOrder()) && !TransactionUtils.isEmployeeMeal(info.getOrder())) {
            scmService.bookWastage(info.getOrder());
        }
        sb.append(String.format("\n----------- ,STEP 12, - ,Book Wastage, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        return info;

    }

    public boolean hasSelectOrder(Order order) {
        return order.getOrders() != null && order.getOrders().size() > 0 && isSubscriptionPresent(order);
    }

    private void sendLoyalTeaNotifications(Order order, OrderInfo info, OrderNotification orderNotification,
                                           Boolean hasSelectOrder) {
        try {
            if (Objects.nonNull(order.getPointsAcquired()) && order.getPointsAcquired() > 0) {
                // Two Cases ---> A. Send System Generated Notification , i this case send loyal
                // tea notif over whatsapp
                // -------------> B. SET Attributes in orderNotification
                // TODO Send Notifications
//			if(properties.getSystemGeneratedNotifications()){
//				if(Objects.nonNull(info.getCustomer().getOptWhatsapp()) && AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())){
//					sendLoyalTeaWhatsappNotification(order,info,orderNotification);
//				}
//			}else{
//				setLoyalTeaNotificationMetadata(info,orderNotification);
//			}
            }
            if (properties.getSystemGeneratedNotifications()) {
                if ((!TransactionUtils.isCODOrder(info.getOrder().getSource())
                        || info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID)
                        && Objects.nonNull(info.getCustomer().getOptWhatsapp())
                        && info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)
                        && !info.getOrder().isGiftCardOrder() && !hasSelectOrder && order.getCustomerId() > 5) {
                    sendWhatsAppNotification(order, info, orderNotification);
                }
            } else {
                setOrderNotificationMetadata(info, orderNotification);
            }
        } catch (Exception e) {
            log.info("Exception ocurred during sendLoyalTeaNotifications for order {} of customer {}",
                    order.getOrderId(), order.getCustomerId());
        }
    }

    private void setOrderNotificationMetadata(OrderInfo info, OrderNotification orderNotification) {
        if (Objects.nonNull(orderNotification)) {
            orderNotification.setOrderAmt(info.getOrder().getTransactionDetail().getPaidAmount());
            orderNotification.setEarnedLoyalTeaPoint(info.getOrder().getEarnedLoyaltypoints());
            orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
            Map<String, String> payload = new HashMap<>();
            updateSavingTextAndSavingAmt(info, payload);
            if (Objects.nonNull(payload) && !payload.isEmpty()) {
                if (payload.containsKey("savingText")) {
                    orderNotification.setSavingText(payload.get("savingText"));
                }
                if (payload.containsKey("savingAmt")) {
                    orderNotification.setSavingAmt(new BigDecimal(payload.get("savingAmt")));
                }
            }
        }
    }

    private void updateSavingTextAndSavingAmt(OrderInfo info, Map<String, String> payload) {
        if (Objects.nonNull(info.getOrder().getOfferCode())) {
            if (productCache.getSubscriptionSkuCodeDetail().containsKey(info.getOrder().getOfferCode())) {
                Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productAlias = unitCacheService
                        .getUnitProductAlias(info.getUnit().getId(), productCache.getSubscriptionSkuCodeDetail()
                                .get(info.getOrder().getOfferCode()).getValue().getId());
                if (Objects.nonNull(productAlias)) {
                    Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> firstKey = productAlias.keySet().stream().findFirst();
                    if (firstKey.isPresent() && productAlias.get(firstKey.get()) != null) {
                        payload.put("savingText", productAlias.get(firstKey.get()));
                    } else {
                        payload.put("savingText", info.getOrder().getOfferCode());
                    }
                } else {
                    payload.put("savingText", info.getOrder().getOfferCode());
                }
            }
            payload.put("savingAmt", info.getOrder().getTransactionDetail().getSavings().toString());
        } else {
            payload.put("savingText", "NO_REASON");
            payload.put("savingAmt", "0.00");
        }
    }

    private void sendWhatsAppNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
        try {
            Map<String, String> payload = new HashMap<>();
            if (!order.getWhatsappNotificationPayload().isEmpty()) {
                payload = order.getWhatsappNotificationPayload();
            }
            payload.put("firstName", info.getCustomer().getFirstName());
            payload.put("cafeName", info.getOrder().getUnitName());
            payload.put("orderAmt", info.getOrder().getTransactionDetail().getPaidAmount().toString());
            payload.put("earnedLoyalTeaPoint", info.getOrder().getEarnedLoyaltypoints().toString());
            payload.put("totalLoyalTeaPoint", String.valueOf(info.getCustomer().getLoyaltyPoints()));
            updateSavingTextAndSavingAmt(info, payload);
            /*
             * if (Objects.nonNull(info.getOrder().getOfferCode())){
             * if(getMasterDataCache().getSubscriptionSkuCodeDetail().containsKey(info.
             * getOrder().getOfferCode())){
             * Map<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>,String>
             * productAlias =
             * getMasterDataCache().getUnitProductAlias(info.getUnit().getId(),
             * getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().
             * getOfferCode()).getValue().getId());
             * Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>> firstKey
             * =productAlias.keySet().stream().findFirst(); if (firstKey.isPresent() &&
             * productAlias.get(firstKey.get())!=null){ payload.put("savingText",
             * productAlias.get(firstKey.get())); }else { payload.put("savingText",
             * info.getOrder().getOfferCode()); } } payload.put("savingAmt",
             * info.getOrder().getTransactionDetail().getSavings().toString()); } else {
             * payload.put("savingText", "NO_REASON"); payload.put("savingAmt", "0.00"); }
             */
            order.setWhatsappNotificationPayload(payload);
            if ((order.getWhatsappNotificationPayloadType().equals("ORDER_WALLET")
                    || order.getWhatsappNotificationPayloadType().equals("ORDER_WALLET_LOYALTEA"))
                    && order.getWhatsappNotificationPayload().containsKey("walletSavingAmount")
                    && order.getWhatsappNotificationPayload().get("walletSavingAmount").equals("0.00")) {
                order.setWhatsappNotificationPayloadType("ORDER");
            }
            if (!properties.getSystemGeneratedNotifications()) {
                orderNotification.setCafeName(info.getOrder().getUnitName());
                orderNotification.setOrderAmt(info.getOrder().getTransactionDetail().getPaidAmount());
                orderNotification.setEarnedLoyalTeaPoint(info.getOrder().getEarnedLoyaltypoints());
                orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
                if (Objects.nonNull(payload) && !payload.isEmpty()) {
                    if (payload.containsKey("savingText")) {
                        orderNotification.setSavingText(payload.get("savingText"));
                    }
                    if (payload.containsKey("savingAmt")) {
                        orderNotification.setSavingAmt(new BigDecimal(payload.get("savingAmt")));
                    }
                }
            } else {
                if (properties.getisDineWhatsappNotificationFlag()
                        || (!(order.getChannelPartner() == AppConstants.CHANNEL_PARTNER_DINE_IN_APP
                        || (info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID
                        && (Objects.nonNull(order.getSourceId())
                        && order.getSourceId().contains(AppConstants.APP)))))) {
                    customerCommunicationEventPublisher.publishCustomerCommunicationEvent(
                            properties.getEnvironmentType().name(),
                            getNotificationPayload(order.getWhatsappNotificationPayloadType(), info,
                                    order.getWhatsappNotificationPayload()));
                }
            }
        } catch (Exception e) {
            log.error("WHATSAPP_NOTIFICATOIN :::Exception Publishing Order Notification on Whatsapp :::: {}",
                    info.getCustomer().getId(), e);
        }
    }

    private NotificationPayload getNotificationPayload(String type, OrderInfo info, Map<String, String> payload) {
        try {
            NotificationPayload load = new NotificationPayload();
            load.setCustomerId(info.getCustomer().getId());
            load.setContactNumber(info.getCustomer().getContactNumber());
            load.setOrderId(info.getOrder().getOrderId());
            load.setMessageType(type);
            load.setSendWhatsapp(false);
            if (Objects.nonNull(info.getCustomer().getOptWhatsapp())) {
                load.setWhatsappOptIn(info.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }
            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(payload);
            return load;
        } catch (Exception e) {
            log.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    info.getOrder().getOrderId());
            return null;
        }
    }

	@Override
	public void publishToOrderInfoCacheSQS(String env, OrderInfo info) throws JMSException {
		Order order = info.getOrder();
		Unit unit = info.getUnit();
		if (isRoutedToAssembly(info.getUnit(), order,order.isGiftCardOrder(),
				order.isSubscriptionOrder())) {
			notifyOverWebsocket(unit,order,order.isGiftCardOrder(),info,order.isSubscriptionOrder());
		}
    }

    private void sendNotificationsPostOrderCreation(Unit unitData, Order order, CreateOrderResult result,
                                                    OrderInfo info, OrderNotification orderNotification, StringBuilder sb) throws JMSException {
        try {
            long startTime = System.currentTimeMillis();

            // 7

            startTime = System.currentTimeMillis();
            runSubscription(order, info, result, orderNotification);
            sb.append(String.format("\n----------- ,STEP 7, - ,Run Subscription, ---------------- ,%d, milliseconds ",
                    System.currentTimeMillis() - startTime));

            // 8

            startTime = System.currentTimeMillis();
            handleCashCard(order, info, orderNotification);
            sb.append(String.format(
                    "\n----------- ,STEP 8"
                            + ", - ,Send Cash Card Notification, ---------------------------- ,%d, milliseconds ",
                    System.currentTimeMillis() - startTime));
            // 9
        } catch (Exception e) {
            log.info("Exception ocurred during sendNotificationsPostOrderCreation for order {} of customer {} ",
                    order.getOrderId(), order.getCustomerId());
        }

    }

	private void notifyOverWebsocket(Unit unitData, Order order, boolean hasGiftcards, OrderInfo info,
			boolean hasSelectOrder) {
		// preparing socket channel to publish to subscribers
		String webSocketChannel = AppConstants.WEB_SOCKET_CHANNEL + order.getUnitId()
				+ AppConstants.WEB_SOCKET_CHANNEL_ORDERS;
        long time = System.currentTimeMillis();
            // adding to orderInfo cache
            orderInfoCache.addToCache(info);
            // add to list of orders which are yet to be delivered via sockets
            orderInfoCache.addToUndelivered(info);

            log.info("Sending to the assembly screen of {} :::::: {}", info.getOrder().getUnitName(),
                    info.getOrder().getGenerateOrderId());
            log.info("Adding to orderInfo cache took  : {}",System.currentTimeMillis()-time);

            if (unitData.isWorkstationEnabled()) {
                 time = System.currentTimeMillis();
                pushOrderNotification(info);
                log.info("----------- Workstation notification took ------------------------------- ,{}, milliseconds ",
                        System.currentTimeMillis() - time);
            } else {
                // publishing to web socket channel subscriber
                 time = System.currentTimeMillis();
                OrderInfo duplicate = new OrderInfo(null, info.getOrder(), info.getCustomer(),
                        info.getDeliveryPartner(), info.getChannelPartner(),
                        unitData.isWorkstationEnabled() ? null : info.getReceipts(), null, info.getDeliveryDetails(),
                        info.getPrintType());
                 template.convertAndSend(webSocketChannel, duplicate);
                log.info("----------- web socket channel subscriber notigication ------------------------------- ,{}, milliseconds ",
                        System.currentTimeMillis() - time);
			}

    }

    public void pushOrderNotification(OrderInfo order) {
        Integer unitId = order.getUnit().getId();
        OrderPushNotification notification = new OrderPushNotification(order.getOrder().getOrderId(),
                order.getOrder().getStatus(), order.getOrder().getSource(), unitId);
        EnvType envType = properties.getEnvironmentType();
        String relayType = TransactionUtils.getAssemblyRelay(unitId, order.getOrder().getSource());
        notification.setTopic(AppUtils.getAssemblyChannelName(envType.name(), unitId, relayType));
        notification.setSendToAndroid(AppConstants.YES);
        try {
            firebaseNotificationService.sendNotification(envType, notification);
        } catch (Exception e) {
            log.error("Error while sending push notification to the client", e);
//			new ErrorNotification("FCM Push Notification Faliure",
//					"Error while sending push notification to the client", e, envType).sendEmail(); TODO gchat
        }
    }

    private void handleCashCard(Order order, OrderInfo info, OrderNotification orderNotification) {
        sendCashCardNotification(order, info, orderNotification);
        sendCashCardPurchaseNotification(order, info, orderNotification);
    }

    protected void runSubscription(Order order, OrderInfo info, CreateOrderResult result,
                                   OrderNotification orderNotification) throws JMSException {
        try {
            if (order.getCustomerId() > 5) {
                if (Objects.nonNull(order.getOfferCode())) {
                    com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> subscriptionObj = productCache
                            .getSubscriptionSkuCodeDetail().get(order.getOfferCode());
                    if (Objects.nonNull(subscriptionObj)) {
                        customerOfferManagementService.addSubscriptionSaving(order.getCustomerId(), order, subscriptionObj);
//				if (Objects.nonNull(info.getCustomer().getOptWhatsapp()) && info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)) {
                        sendWhatsappNOtificationForChaayosSelectSaving(order, info, orderNotification);
//				}
                    }
                }
                    if (result.getSubscriptionPlan() != null) {
                        int subscriptionProduct = 0;
                        for (OrderItem item : info.getOrder().getOrders()) {
                            if (Objects.nonNull(productCache.getSubscriptionProductDetail(item.getProductId()))) {
                                subscriptionProduct = item.getProductId();
                                break;
                            }
                        }
                        sendSubscriptionPurchaseNotification(result.getSubscriptionPlan(), info.getBrand(),
                                info.getCustomer(), subscriptionProduct, info.getOrder().getUnitId(),
                                info.getOrder().getOrderId(), order.getChannelPartner(), orderNotification);
                    }
            }

        } catch (Exception e) {
            log.error(
                    "WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification on whatsapp :: {}",
                    info.getCustomer().getId(), e);
        }
    }

    private void sendSubscriptionPurchaseNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
                                                      Integer subscriptionProduct, Integer unitId, Integer orderId, int channelPartnerId,
                                                      OrderNotification orderNotification) {
        if (Objects.nonNull(subscriptionPlan)) {
            SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
            SubscriptionOfferInfoDetail subscriptionInfoDetail = mappingCache
                    .getSubscriptionInfoDetail(subscriptionPlan);
            if (Objects.nonNull(subscriptionInfoDetail)) {
                log.info("Subscription SMS Details to be send to customer :: {}", customer.getContactNumber());
                sendSubscriptionNotification(subscriptionPlan, brand, customer, smsWebServiceClient,
                        subscriptionInfoDetail, subscriptionProduct, unitId, orderId, channelPartnerId,
                        orderNotification);
            }
        }

    }

    private boolean sendSubscriptionUsageNotification(SubscriptionViewData subscriptionView,
                                                      SMSWebServiceClient smsWebServiceClient, OrderInfo info, Map<String, String> map) {
        try {
            String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED.getMessage(subscriptionView);
            return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED.name(),
                    message, info.getCustomer().getContactNumber(), smsWebServiceClient,
                    properties.getAutomatedNPSSMS(),
                    getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED, info.getCustomer(),
                            map, info.getOrder().getOrderId()));
        } catch (Exception e) {
            log.error("Error while sending Subscription SMS to Customer :: {}", info.getCustomer().getContactNumber());
            return false;
        }
    }

    protected void sendSubscriptionNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
                                                SMSWebServiceClient smsWebServiceClient, SubscriptionOfferInfoDetail subscriptionInfoDetail,
                                                Integer subscriptionProduct, Integer unitId, Integer orderId, int channelPartnerId,
                                                OrderNotification change) {
        try {
            String subscriptionName = getSubscriptionName(subscriptionProduct, unitId);
            subscriptionInfoDetail.setCustomerName(customer.getFirstName());
            subscriptionInfoDetail.setValidDays((int) AppUtils.getActualDayDifference(AppUtils.getBusinessDate(),
                    subscriptionPlan.getPlanEndDate()));
            subscriptionInfoDetail.setExpiryDate(subscriptionPlan.getPlanEndDate());
            subscriptionInfoDetail
                    .setSubscriptionUrl(Objects.nonNull(brand.getChaayosSubscription()) ? brand.getChaayosSubscription()
                            : "chaayos.com/pages/chaayos-select");
            Map<String, String> map = new HashMap<>();
            map.put("firstName", customer.getFirstName());
            map.put("productName", subscriptionName);
            map.put("validityInDays", subscriptionInfoDetail.getValidDays().toString());
            map.put("offerDescription", subscriptionInfoDetail.getOfferText());
            map.put("endDate", AppUtils.getDateInMonth(subscriptionInfoDetail.getExpiryDate()));
            SubscriptionViewData subscriptionViewData = getSubscriptionView(subscriptionInfoDetail);
            if (properties.getSystemGeneratedNotifications()) {
                sendSubscriptionNotification(subscriptionViewData, smsWebServiceClient, customer, map, orderId,
                        channelPartnerId);
            } else {
                getSubscriptionNotificationMetaData(subscriptionViewData, change, null, subscriptionInfoDetail, true,
                        null,subscriptionPlan);
            }
        } catch (Exception e) {
            log.error("WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification :: {}",
                    customer.getId(), e);
        }
    }

    private SubscriptionViewData getSubscriptionView(SubscriptionOfferInfoDetail subscriptionInfoDetail) {
        SubscriptionViewData subscriptionViewData = new SubscriptionViewData();
        subscriptionViewData.setCustomerName(subscriptionInfoDetail.getCustomerName());
        com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> couponMapping = productCache
                .getSubscriptionSkuCodeDetail(subscriptionInfoDetail.getSubscriptionCode());
        subscriptionViewData.setSubscriptionName(couponMapping.getValue().getName());
        subscriptionViewData.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
        subscriptionViewData.setValidityDays(subscriptionInfoDetail.getValidDays());
        subscriptionViewData.setPlanEndDate(subscriptionInfoDetail.getExpiryDate());
        return subscriptionViewData;
    }

    private String getSubscriptionName(Integer subscriptionProduct, Integer unitId) {
        try {

            Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productVOS = productCache
                    .getUnitProductAlias(unitId, subscriptionProduct);
            if (!productVOS.isEmpty()) {
                Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> firstKey = productVOS.keySet()
                        .stream().findFirst();
                if (firstKey.isPresent() && productVOS.get(firstKey.get()) != null) {
                    return productVOS.get(firstKey.get());
                } else {
                    return productCache.getProduct(subscriptionProduct).getName();
                }
            } else {
                return productCache.getProduct(subscriptionProduct).getName();
            }
        } catch (Exception e) {
            log.error("CHAAYOS_SUBSCRIPTION ::: Exception Faced While Fetching Subscription Name", e);
        }
        return "Chaayos Membership";
    }

    protected boolean sendSubscriptionNotification(SubscriptionViewData subscriptionInfoDetail,
                                                   SMSWebServiceClient smsWebServiceClient, Customer customer, Map<String, String> map, Integer orderId,
                                                   int channelPartnerId) throws IOException {
        try {
            String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.getMessage(subscriptionInfoDetail);
            return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.name(),
                    message, customer.getContactNumber(), smsWebServiceClient, properties.getAutomatedNPSSMS(),
                    getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION, customer, map, orderId,
                            channelPartnerId));
        } catch (Exception e) {
            log.error("Error while sending Subscription SMS to Customer :: {}", customer.getContactNumber());
            return false;
        }
    }

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
                                                       Map<String, String> map, Integer orderId, int channelPartnerId) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(customer)) {
                load.setCustomerId(customer.getId());
            }
            load.setContactNumber(customer.getContactNumber());
            load.setOrderId(orderId);
            load.setMessageType(type.name());
            if (channelPartnerId == AppConstants.CHANNEL_PARTNER_DINE_IN_APP) {
                load.setSendWhatsapp(properties.getisDineWhatsappNotificationFlag());
            } else {
                load.setSendWhatsapp(type.isWhatsapp());
            }
            if (Objects.nonNull(customer.getOptWhatsapp())) {
                load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }

            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            log.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderId);
            return null;
        }
    }

    private OrderNotification getSubscriptionNotificationMetaData(SubscriptionViewData subscriptionViewData,
                                                                  OrderNotification orderNotification, Order order, SubscriptionOfferInfoDetail subscriptionOfferInfoDetail,
                                                                  Boolean isSelectPurchased, Boolean isSelectUsed , SubscriptionPlan subscriptionPlan) {
        if (Objects.nonNull(subscriptionViewData)) {
            orderNotification.setSubscriptionName(subscriptionViewData.getSubscriptionName());
            orderNotification.setSelectOverallSaving(subscriptionViewData.getTotalSaving());
            orderNotification.setPlanEndDate(AppUtils.formatDate(subscriptionViewData.getPlanEndDate(),AppConstants.DATE_FORMAT));
            orderNotification.setCustomerName(subscriptionViewData.getCustomerName());
            orderNotification.setDaysLeft(subscriptionViewData.getValidityDays());
            if (Objects.nonNull(subscriptionOfferInfoDetail)
                    && Objects.nonNull(subscriptionOfferInfoDetail.getValidDays())
                    && subscriptionOfferInfoDetail.getValidDays() > 0) {
                orderNotification.setSubscriptionValidityInDays(subscriptionOfferInfoDetail.getValidDays());
            }
            orderNotification.setIsSubscriptionPurched(isSelectPurchased);
            orderNotification.setIsSubscriptionUsed(isSelectUsed);
            if (Objects.nonNull(order) && Objects.nonNull(order.getTransactionDetail())
                    && Objects.nonNull(order.getTransactionDetail().getSavings())
                    && BigDecimal.ZERO.compareTo(order.getTransactionDetail().getSavings()) < 0) {
                orderNotification.setSelectSavingAmount(order.getTransactionDetail().getSavings());
            }
            orderNotification.setOfferDescription(Objects.nonNull(subscriptionOfferInfoDetail)
                    && Objects.nonNull(subscriptionOfferInfoDetail.getOfferText())
                    ? subscriptionOfferInfoDetail.getOfferText()
                    : subscriptionViewData.getOfferDescription());
            if(Objects.nonNull(subscriptionPlan) && StringUtils.isNotBlank(subscriptionPlan.getFrequencyStrategy()) ){
                if(subscriptionPlan.getFrequencyStrategy().equals(FrequencyOfferType.QUANTITY_BASED.name()) || subscriptionPlan.getFrequencyStrategy().equals(FrequencyOfferType.TIME_QUANTITY_BASED.name())){
                    if(Objects.nonNull(subscriptionPlan.getFrequencyLimit()) && subscriptionPlan.getFrequencyLimit().compareTo(BigDecimal.valueOf(Integer.MAX_VALUE)) == -1 && Objects.nonNull(subscriptionPlan.getOverAllFrequency())){
                        orderNotification.setChaiLeft(subscriptionPlan.getFrequencyLimit().subtract(subscriptionPlan.getOverAllFrequency()).intValue());
                    }
                }
            }
        }
        return orderNotification;
    }

    private void sendWhatsappNOtificationForChaayosSelectSaving(Order order, OrderInfo info,
                                                                OrderNotification orderNotification) throws JMSException {
        try {
            Map<String, String> map = new HashMap<>();
            Map<String, String> orderPayloadMap = new HashMap<>();
            getSubscriptionName(info, map);
            orderPayloadMap = map;
            SubscriptionInfoDetail subscriptionInfoDetail = customerOfferManagementService
                    .getSubscriptionInfoDetail(info.getCustomer().getId());
            if(Objects.nonNull(subscriptionInfoDetail)){
                if(StringUtils.isNotBlank(subscriptionInfoDetail.getFrequencyStrategy()) ){
                    if(subscriptionInfoDetail.getFrequencyStrategy().equals(FrequencyOfferType.QUANTITY_BASED.name()) || subscriptionInfoDetail.getFrequencyStrategy().equals(FrequencyOfferType.TIME_QUANTITY_BASED.name())){
                        if(Objects.nonNull(subscriptionInfoDetail.getFrequencyLimit()) && subscriptionInfoDetail.getFrequencyLimit().compareTo(BigDecimal.valueOf(Integer.MAX_VALUE)) == -1 && Objects.nonNull(subscriptionInfoDetail.getOverAllFrequency())){
                            orderNotification.setChaiLeft(subscriptionInfoDetail.getFrequencyLimit().subtract(subscriptionInfoDetail.getOverAllFrequency()).intValue());
                        }
                    }
                }
                map.put("overAllSaving", subscriptionInfoDetail.getOverAllSaving().toString());
                orderPayloadMap.put("selectOverAllSaving", subscriptionInfoDetail.getOverAllSaving().toString());
                SubscriptionViewData subscriptionViewData = getSubscriptionView(subscriptionInfoDetail, info);
                if (!properties.getSystemGeneratedNotifications()) {
                    getSubscriptionNotificationMetaData(subscriptionViewData, orderNotification, order, null, null, true,null);
                } else {
                    if (Objects.isNull(info.getCustomer().getOptWhatsapp())
                            || (Objects.nonNull(info.getCustomer().getOptWhatsapp())
                            && info.getCustomer().getOptWhatsapp().equals(AppConstants.NO))) {
                        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient
                                .getTransactionalClient(info.getBrand());
                        sendSubscriptionUsageNotification(subscriptionViewData, smsWebServiceClient, info, map);
                    }
                }
            }
            map.put("generateOrderId", info.getOrder().getGenerateOrderId());
            map.put("savingAmount", order.getTransactionDetail().getSavings().toString());
            orderPayloadMap.put("generateOrderId", info.getOrder().getGenerateOrderId());
            orderPayloadMap.put("selectSavingAmount", order.getTransactionDetail().getSavings().toString());
            order.setWhatsappNotificationPayload(orderPayloadMap);
            String type = "SELECT";
            if (!order.getWhatsappNotificationPayloadType().isEmpty()) {
                type = order.getWhatsappNotificationPayloadType() + "_" + type;
            }
            order.setWhatsappNotificationPayloadType(type);
        } catch (Exception e) {
            log.error(
                    "WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription saving notification on whatsapp :: {}",
                    info.getCustomer().getId(), e);
        }
    }

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
                                                       Map<String, String> map, Integer orderId) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(customer)) {
                load.setCustomerId(customer.getId());
            }
            load.setContactNumber(customer.getContactNumber());
            load.setOrderId(orderId);
            load.setMessageType(type.name());
            load.setSendWhatsapp(type.isWhatsapp());
            if (Objects.nonNull(customer.getOptWhatsapp())) {
                load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }

            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            log.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderId);
            return null;
        }
    }

    private SubscriptionViewData getSubscriptionView(SubscriptionInfoDetail subscriptionInfoDetail, OrderInfo info) {
        SubscriptionViewData subscriptionViewData = new SubscriptionViewData();
        subscriptionViewData.setCustomerName(info.getCustomer().getFirstName());
        com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> couponMapping = productCache
                .getSubscriptionSkuCodeDetail(subscriptionInfoDetail.getSubscriptionCode());
        subscriptionViewData.setSubscriptionName(couponMapping.getValue().getName());
        subscriptionViewData.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
        subscriptionViewData.setValidityDays(subscriptionInfoDetail.getDaysLeft());
        subscriptionViewData.setPlanEndDate(subscriptionInfoDetail.getEndDate());
        subscriptionViewData.setTotalSaving(subscriptionInfoDetail.getOverAllSaving().intValue());
        return subscriptionViewData;
    }

    private void getSubscriptionName(OrderInfo info, Map<String, String> map) {
        try {
            Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productVOS = productCache
                    .getUnitProductAlias(info.getUnit().getId(), productCache
                            .getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getId());
            if (!productVOS.isEmpty()) {
                Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> firstKey = productVOS.keySet()
                        .stream().findFirst();
                if (firstKey.isPresent() && productVOS.get(firstKey.get()) != null) {
                    map.put("productName", productVOS.get(firstKey.get()));
                } else {
                    map.put("productName", productCache.getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode())
                            .getValue().getName());
                }
            } else {
                map.put("productName",
                        productCache.getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getName());
            }
        } catch (Exception e) {
            log.error("CHAAYOS_SUBSCRIPTIOM ::: Exception Faced While Fetching Chaayos Subscription Name", e);
            map.put("productName", "Chaayos Select");
        }

    }

    private void sendCashCardNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
        boolean cashCardRedemption = false;
        BigDecimal settledValue = BigDecimal.ZERO;
        if (info.getCustomer().getId() > 5) {
            for (Settlement settlement : info.getOrder().getSettlements()) {
                if (settlement.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
                    cashCardRedemption = true;
                    settledValue = settlement.getAmount();
                    break;
                }
            }
        }
        if (cashCardRedemption) {
            sendCashCardRedemptionNotification(order, info, settledValue, orderNotification);
        }
    }

    private void sendCashCardRedemptionNotification(Order order, OrderInfo info, BigDecimal settledValue,
                                                    OrderNotification orderNotification) {
        sendCashCardNotification(order, info, BigDecimal.ZERO, CashCardSMSNotificationType.CASH_CARD_REDEMPTION,
                settledValue, BigDecimal.ZERO, null, null, orderNotification);
    }

    private void sendCashCardNotification(Order order, OrderInfo info, BigDecimal refundAmount,
                                          CashCardSMSNotificationType type, BigDecimal settledAmount, BigDecimal cashBack, String startDate,
                                          String endDate, OrderNotification orderNotification) {
        String fName = info.getCustomer().getFirstName();
        if (StringUtils.isNotBlank(fName)) {
            fName = Character.toString(fName.charAt(0)).toUpperCase() + fName.substring(1);
        }
        List<CashCardDetail> card = cardService.getActiveCashCards(info.getCustomer().getId());
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal initialAmount = BigDecimal.ZERO;
        BigDecimal extraAmount = BigDecimal.ZERO;
        for (CashCardDetail c : card) {
            pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount()); // var1
            initialAmount = AppUtils.add(initialAmount, c.getCashInitialAmount());
            extraAmount = AppUtils.add(extraAmount, c.getInitialOffer());
        }
        BigDecimal percentageOffer = AppUtils.percentage(extraAmount, initialAmount);
        BigDecimal savingAmount = AppUtils.percentageOf(percentageOffer, settledAmount);
        CashCardNotificationData data = new CashCardNotificationData();
        data.setCustomerName(fName);
        data.setPendingAmount(pendingAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        data.setUnitName(info.getUnit().getName());
        data.setRefundAmount(refundAmount);
        data.setUsedAmount(settledAmount);
        data.setCashBackAmount(cashBack);
        data.setStartDate(startDate);
        data.setEndDate(endDate);
        try {
            Map<String, String> payloadMap = new HashMap<>();
            String payloadType = "WALLET";
            Map<String, String> orderPayloadMap = new HashMap<>();
            if (!order.getWhatsappNotificationPayload().isEmpty()) {
                orderPayloadMap = order.getWhatsappNotificationPayload();
            }
            if (!order.getWhatsappNotificationPayloadType().isEmpty()) {
                payloadType = order.getWhatsappNotificationPayloadType() + "_" + payloadType;
            }
            payloadMap.put("pendingAmount", pendingAmount.toString());// v1
            payloadMap.put("savingAmount", savingAmount.toString());// v2
            payloadMap.put("generateOrderId", info.getOrder().getGenerateOrderId());// v2
            orderPayloadMap.put("walletPendingAmount", pendingAmount.toString());// v1
            orderPayloadMap.put("walletSavingAmount", savingAmount.toString());// v2
            if (!orderPayloadMap.containsKey("generateOrderId")) {
                orderPayloadMap.put("generateOrderId", info.getOrder().getGenerateOrderId());// v
            }
            order.setWhatsappNotificationPayload(orderPayloadMap);
            order.setWhatsappNotificationPayloadType(payloadType);
            String message = type.getMessage(data);
//			if(Objects.isNull(info.getCustomer().getOptWhatsapp()) || (Objects.nonNull(info.getCustomer().getOptWhatsapp()) && info.getCustomer().getOptWhatsapp().equals(AppConstants.NO))){
            if (properties.getSystemGeneratedNotifications()) {
                notificationService.sendNotification(type.name(), message, info.getCustomer().getContactNumber(),
                        providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                        properties.getSendAutomatedOTPSMS(), getNotificationPayload(type, info, payloadMap));
            } else {
                orderNotification.setWalletSavingAmount(savingAmount.toString());
                orderNotification.setWalletUsed(Boolean.TRUE);
                getCashCardRedemptionNotificationMetaData(orderNotification, data);
            }
//			}

        } catch (IOException | JMSException e) {
            log.error("WHATSAPP_NOTIFICATOIN :::Error while sending the Cash Card Redemption message for "
                    + info.getOrder().getOrderId(), e);
        }

    }

    private OrderNotification getCashCardRedemptionNotificationMetaData(OrderNotification orderNotification,
                                                                        CashCardNotificationData cashCardNotificationData) {
        long time = System.currentTimeMillis();
        if (Objects.nonNull(cashCardNotificationData)) {
            orderNotification.setUsedAmount(cashCardNotificationData.getUsedAmount().intValue());
            orderNotification.setCafeName(cashCardNotificationData.getUnitName());
            orderNotification.setWalletPendingAmount(cashCardNotificationData.getPendingAmount().intValue());
        }
        log.info("<--------Setting Cash Card Redemption Notification metadata took :{} ms-------->",
                System.currentTimeMillis() - time);
        return orderNotification;
    }

    private NotificationPayload getNotificationPayload(CashCardSMSNotificationType type, OrderInfo orderInfo,
                                                       Map<String, String> map, int channelPartnerId) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(orderInfo.getCustomer())) {
                load.setCustomerId(orderInfo.getCustomer().getId());
            }
            load.setOrderId(orderInfo.getOrder().getOrderId());
            load.setContactNumber(orderInfo.getCustomer().getContactNumber());
            load.setMessageType(type.name());
            if (channelPartnerId == AppConstants.CHANNEL_PARTNER_DINE_IN_APP) {
                load.setSendWhatsapp(properties.getisDineWhatsappNotificationFlag());
            } else {
                load.setSendWhatsapp(type.isWhatsapp());
            }
            if (Objects.nonNull(orderInfo.getCustomer().getOptWhatsapp())) {
                load.setWhatsappOptIn(orderInfo.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }
            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            log.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderInfo.getOrder().getOrderId());
            return null;
        }
    }

    private NotificationPayload getNotificationPayload(CashCardSMSNotificationType type, OrderInfo orderInfo,
                                                       Map<String, String> map) {
        try {
            NotificationPayload load = new NotificationPayload();
            if (Objects.nonNull(orderInfo.getCustomer())) {
                load.setCustomerId(orderInfo.getCustomer().getId());
            }
            load.setOrderId(orderInfo.getOrder().getOrderId());
            load.setContactNumber(orderInfo.getCustomer().getContactNumber());
            load.setMessageType(type.name());
            load.setSendWhatsapp(type.isWhatsapp());
            if (Objects.nonNull(orderInfo.getCustomer().getOptWhatsapp())) {
                load.setWhatsappOptIn(orderInfo.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
            } else {
                load.setWhatsappOptIn(false);
            }
            load.setRequestTime(AppUtils.getCurrentTimestamp());
            load.setPayload(map);
            return load;
        } catch (Exception e) {
            log.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
                    orderInfo.getOrder().getOrderId());
            return null;
        }
    }

    private void sendCashCardNotification(OrderInfo info, BigDecimal refundAmount, CashCardSMSNotificationType type,
                                          BigDecimal settledAmount, BigDecimal cashBack, String startDate, String endDate,
                                          OrderNotification orderNotification) {
        String fName = info.getCustomer().getFirstName();
        if (StringUtils.isNotBlank(fName)) {
            fName = Character.toString(fName.charAt(0)).toUpperCase() + fName.substring(1);
        }
        List<CashCardDetail> card = cardService.getActiveCashCards(info.getCustomer().getId());
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal initialAmount = BigDecimal.ZERO;
        BigDecimal extraAmount = BigDecimal.ZERO;
        for (CashCardDetail c : card) {
            pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount()); // var1
            initialAmount = AppUtils.add(initialAmount, c.getCashInitialAmount());
            extraAmount = AppUtils.add(extraAmount, c.getInitialOffer());
        }
        BigDecimal percentageOffer = AppUtils.percentage(extraAmount, initialAmount);
        BigDecimal savingAmount = AppUtils.percentageOf(percentageOffer, settledAmount);
        CashCardNotificationData data = new CashCardNotificationData();
        data.setCustomerName(fName);
        data.setPendingAmount(pendingAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        data.setUnitName(info.getUnit().getName());
        data.setRefundAmount(refundAmount);
        data.setUsedAmount(settledAmount);
        data.setCashBackAmount(cashBack);
        data.setStartDate(startDate);
        data.setEndDate(endDate);
        orderNotification = getCashCardCashBackAllotmentNotificationMetaData(orderNotification, data);
        try {
            Map<String, String> payloadMap = new HashMap<>();
            payloadMap.put("pendingAmount", pendingAmount.toString());// v1
            payloadMap.put("savingAmount", savingAmount.toString());// v2
            payloadMap.put("generateOrderId", info.getOrder().getGenerateOrderId());// v2
            String message = type.getMessage(data);
            if (properties.getSystemGeneratedNotifications() || type.name().equals("CASH_CARD_REFUND")) {// since refund
                // flow to
                // be made
                notificationService.sendNotification(type.name(), message, info.getCustomer().getContactNumber(),
                        providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                        properties.getSendAutomatedOTPSMS(), getNotificationPayload(type, info, payloadMap));
            } else {
                // cashCardCashbackAllotment and cashCardRefund goes through here
                if (type.name().equals("CASH_CARD_CASH_BACK_ALLOTMENT")) {
                    getCashCardCashBackAllotmentNotificationMetaData(orderNotification, data);
                }
            }
        } catch (IOException | JMSException e) {
            log.error("WHATSAPP_NOTIFICATOIN :::Error while sending the Cash Card Redemption message for "
                    + info.getOrder().getOrderId(), e);
        }
    }

    private OrderNotification getCashCardCashBackAllotmentNotificationMetaData(OrderNotification orderNotification,
                                                                               CashCardNotificationData cashCardNotificationData) {
        if (Objects.nonNull(orderNotification) && Objects.nonNull(cashCardNotificationData)) {
            orderNotification.setCashBackAmount(cashCardNotificationData.getCashBackAmount());
            orderNotification.setCashBackAllotmentStartDate(cashCardNotificationData.getStartDate());
            orderNotification.setCashBackAllotmentEndDate(cashCardNotificationData.getEndDate());
        }
        return orderNotification;
    }

    private void sendCashBackNotification(OrderInfo info, BigDecimal cashBack, String startDate, String endDate,
                                          OrderNotification orderNotification) {

        sendCashCardNotification(info, BigDecimal.ZERO, CashCardSMSNotificationType.CASH_CARD_CASH_BACK_ALLOTMENT,
                BigDecimal.ZERO, cashBack, startDate, endDate, orderNotification);

    }

    private void sendCashCardPurchaseNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
        if (order.getCustomerId() > 5) {
            List<OrderItem> cards = new ArrayList<>();
            for (OrderItem item : order.getOrders()) {
                if (AppUtils.isGiftCard(item.getCode())) {
                    cards.add(item);
                }
            }
            if (!cards.isEmpty()) {
                List<CashCardDetail> card = cardService.getActiveCashCards(info.getCustomer().getId());
                BigDecimal pendingAmount = BigDecimal.ZERO;
                BigDecimal pendingAmountOfOrder = BigDecimal.ZERO;
                BigDecimal purchaseAmount = BigDecimal.ZERO;
                for (CashCardDetail c : card) {
                    pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount());
                    if (Objects.equals(info.getOrder().getOrderId(), c.getPurchaseOrderId())) {
                        BigDecimal extraAmount = AppUtils.subtract(c.getCashPendingAmount(), c.getCashInitialAmount());
                        pendingAmountOfOrder = AppUtils.add(pendingAmountOfOrder, extraAmount);
                    }
                }
                boolean sendNotification = false;
                for (OrderItem item : cards) {
                    try {
                        if (CashCardType.GYFTR.name().equals(item.getCardType())) {
                            String message = TransactionUtils.getGyftrPurchaseCashCardMessage(info, item,
                                    cardService.getCardDetail(info.getCustomer().getId(), item.getItemCode(), false),
                                    pendingAmount);
                            CashCardSMSNotificationType type = CashCardSMSNotificationType.CASH_CARD_PURCHASE;

                            if (properties.getSystemGeneratedNotifications()) {
                                notificationService.sendNotification(type.name(), message,
                                        info.getCustomer().getContactNumber(),
                                        SolsInfiniWebServiceClient.getTransactionalClient(), true, null);
                            } else {
                                orderNotification.setCustomerName(info.getCustomer().getFirstName());
                                orderNotification.setItemCode(item.getItemCode().toUpperCase());
                                orderNotification.setCashPendingAmount(cardService
                                        .getCardDetail(info.getCustomer().getId(), item.getItemCode(), false)
                                        .getCashPendingAmount().setScale(0, BigDecimal.ROUND_HALF_UP).toString());
                                orderNotification.setVoucherCode(item.getVoucherCode());
                                orderNotification.setSmsTemplateDate(AppUtils.getSMSTemplateDate(item.getValidUpto()));
                                orderNotification.setWalletPendingAmount(
                                        pendingAmount.setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
                            }
                        } else {
                            purchaseAmount = AppUtils.add(purchaseAmount, item.getAmount());
                            sendNotification = true;
                        }
                    } catch (Exception e) {
                        log.error("Error while sending notification to the customer for cash card purchase : "
                                + info.getOrder().getOrderId(), e);
                    }
                }
                if (sendNotification) {
                    try {
                        Map<String, String> payload = new HashMap<>();
                        payload.put("firstName", info.getCustomer().getFirstName());
                        payload.put("purchaseAmount", purchaseAmount.toString());
                        payload.put("extraAmount", pendingAmountOfOrder.toString());
                        CashCardNotificationData notification = new CashCardNotificationData();
                        notification.setPendingAmount(pendingAmount);
                        notification.setPurchaseAmount(purchaseAmount);
                        notification.setCustomerName(info.getCustomer().getFirstName());

                        CashCardSMSNotificationType type = CashCardSMSNotificationType.CASH_CARD_PURCHASE;
                        String message = type.getMessage(notification);

                        if (properties.getSystemGeneratedNotifications()) {
                            notificationService.sendNotification(type.name(), message,
                                    info.getCustomer().getContactNumber(),
                                    providerService.getSMSClient(type.getTemplate().getSMSType(),
                                            ApplicationName.KETTLE_SERVICE),
                                    properties.getSendAutomatedOTPSMS(),
                                    getNotificationPayload(CashCardSMSNotificationType.CASH_CARD_PURCHASE, info,
                                            payload, order.getChannelPartner()));
                        } else {
                            orderNotification.setWalletExtraAmount(pendingAmountOfOrder);
                            getCashCardPurchaseNotificationMetaData(orderNotification, notification);
                        }
                    } catch (Exception e) {
                        log.error(
                                "WHATSAPP_NOTIFICATOIN ::Error while sending notification to the customer for cash card purchase : "
                                        + info.getOrder().getOrderId(),
                                e);
                    }
                }

            }
        }
    }

    private OrderNotification getCashCardPurchaseNotificationMetaData(OrderNotification orderNotification,
                                                                      CashCardNotificationData cashCardNotificationData) {
        if (Objects.nonNull(cashCardNotificationData)) {
            orderNotification.setCustomerName(cashCardNotificationData.getCustomerName());
            orderNotification.setWalletPurchaseAmt(cashCardNotificationData.getPurchaseAmount().intValue());
            orderNotification.setWalletPendingAmount(cashCardNotificationData.getPendingAmount().intValue());
            orderNotification.setIsWalletPurchased(Boolean.TRUE);
        }
        return orderNotification;
    }

    private void updateAndPublishInventory(Order order, CreateOrderResult result, OrderInfo info,
                                           OrderNotification orderNotification, StringBuilder sb) {
        long startTime = System.currentTimeMillis();
        updateUnitInventory(order, order.getOrderId(), false);
        sb.append(String.format(
                "\n----------- ,STEP 9, - ,Inventory Updated, ---------------------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));


        // 10

        startTime = System.currentTimeMillis();
        // Publish
        publish(info);
        sb.append(String.format(
                "\n----------- ,STEP 10, - ,Order Published for analytics, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // 11
        startTime = System.currentTimeMillis();
        // Publish
        publishInventory(info);
        sb.append(String.format(
                "\n----------- ,STEP 11, - ,Order Published for inventory, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // Publish
        // 12

    }

    private void generateOrderNotifcationEvent(OrderInfo info){
        Map<NotificationType,Boolean> hashmap = new HashMap<>();
        hashmap.put(NotificationType.EMAIL,false);
        info.setCommunicationType(hashmap);
    }

    private OrderInfo generateAndSaveMetadataPostOrderCreation(Order order, CreateOrderResult result,
                                                               Boolean includeReceipts, Boolean newCustomer, StringBuilder sb)
            throws DataNotFoundException, TemplateRenderingException {
        long startTime = System.currentTimeMillis();

        // saving customer info brandwise
        boolean newBrandCustomer = saveCustomerInfoBrandWise(order, result);

        int orderId = result.getOrderId();
        sb.append(String.format(
                "\n----------- ,STEP 2, - ,Order Persisted to DB, ------------------------ ,%d, milliseconds, %d ",
                System.currentTimeMillis() - startTime, orderId));
        log.info("Created order with ID  " + orderId);

        startTime = System.currentTimeMillis();
        Pair<String, String> feedbackUrl = null;
        feedbackUrl = feedbackManagementService.createFeedbackUrl(result);
        sb.append(String.format(
                "\n----------- ,STEP 3, - ,Created Feedback URL, ------------------------ ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        // 3
        startTime = System.currentTimeMillis();
        OrderInfo info = createOrderInfo(orderId, order, includeReceipts, newBrandCustomer, feedbackUrl, result,
                newCustomer);
        sb.append(String.format(
                "\n----------- ,STEP 4, - ,Created Order Info, --------------------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // 4

        return info;

    }

    private void updateMetadataPostOrderCreation(Order order, CreateOrderResult result, OrderInfo info,
                                                 StringBuilder sb) {

        long startTime = System.currentTimeMillis();
        updateOfferCode(order, result.getOrderId(), info);
        sb.append(String.format(
                "\n----------- ,STEP 5, - ,Offer Code related updates, ------------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        // 5
        startTime = System.currentTimeMillis();
        addDeliveryPartner(result.getOrderId(), order, info);
        sb.append(String.format(
                "\n----------- ,STEP 6, - ,Added Delivery Partner, ----------------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // 6

    }

    public void publishInventory(OrderInfo info) {
        if (TransactionUtils.isSpecialOrder(info.getOrder()) && !TransactionUtils.isPaidEmployeeMeal(info.getOrder())) {
            publishInventory(info.getOrder(), InventoryAction.REMOVE, InventorySource.COMPLIMENTARY_ORDER);
        } else {
            publishInventory(info.getOrder(), InventoryAction.REMOVE, InventorySource.CAFE_ORDER);
        }
    }

    public void publishInventory(Order order, InventoryAction action, InventorySource source) {
        log.info("Calculating item Consumption for Generated Order ID = " + order.getOrderId());
        try {
            /*
             * int deliveryUnitId = getMasterDataCache().getDeliveryUnit(order.getUnitId(),
             * TransactionUtils
             * .isPartnetOrder(getMasterDataCache().getChannelPartner(order.
             * getChannelPartner()).getType()));
             */
                int deliveryUnitId = unitCacheService.getDeliveryUnit(order.getUnitId(), order.getChannelPartner(),
                        order.getBrandId(), TransactionUtils.isCODOrder(order.getSource()));
                Map<Integer, ProductQuantityData> map = itemConsumptionHelper.getCriticalConsumption(order, deliveryUnitId);
                if (map != null && map.size() > 0) {
                    QuantityResponseData response = new QuantityResponseData(order.getUnitId(),
                            new ArrayList<>(map.values()), action, source, order.getOrderId(),
                            order.getBillingServerTime());
                    service.publishInventorySQSFifo(properties.getEnvironmentType().name(), response);
                }

        } catch (Exception e) {
            log.error("Error while calculating Consumption for Generated Order ID = " + order.getOrderId(), e);
        }

    }

    protected void publish(OrderInfo info) {
        if (!TransactionUtils.isTableOrder(info.getOrder())) {

            if (properties.publishOrders().get(info.getOrder().getBrandId())) {
                try {
                    OrderResponse response = new OrderResponse(
                            unitCacheService.getUnitBasicDetailById(info.getOrder().getUnitId()), info.getOrder(),
                            info.getCustomer(), info.getDeliveryDetails(), info.getCommunicationType());
                    sqsNotificationService.publishToSQS(properties.getEnvironmentType().name(),response,"_ORDERS" );
                } catch (JMSException e) {
                    log.error("Error while adding order to the message queue", e);
                }
            }
        }

    }

    protected void updateUnitInventory(Order order, Integer orderId, boolean isCancellation) {
        try {

            UnitBasicDetail unit = unitCacheService.getUnitBasicDetailById(order.getUnitId());
            // Non live inventory Units
            if (properties.getTrackInventory() && !unit.isLiveInventoryEnabled()) {
                List<OrderItem> itemList = new ArrayList<>();
                for (OrderItem item : order.getOrders()) {
                    if (!(StringUtils.isNotBlank(item.getCardType())
                            && CashCardType.ECARD.name().equalsIgnoreCase(item.getCardType()))
                            && !voucherService.isGyftrCard(item.getCardType())) {
                        itemList.add(item);
                    }
                }
                List<ProductInventory> inventoryUpdate = getProductInventoryUpdates(unit, itemList);
                InventoryUpdateEvent event = new InventoryUpdateEvent();
                event.setUnitId(order.getUnitId());
                event.getCurrentInventory().addAll(inventoryUpdate);
                inventoryService.updateUnitInventory(event, false, true, order.getEmployeeId(), orderId,
                        isCancellation);
            }
            // live inventory sync
            if (unit.isLiveInventoryEnabled() && isCancellation) {
                publishInventory(order, InventoryAction.ADD, InventorySource.NON_WASTAGE_ORDER_CANCELLATION);
            }
        } catch (Exception e) {
            log.error("Error While Updating Inventory", e);
        }
    }

    private List<ProductInventory> getProductInventoryUpdates(UnitBasicDetail unit, List<OrderItem> items) {
        List<ProductInventory> list = new ArrayList<>();
        for (OrderItem item : items) {
            ProductBasicDetail detail = productCache.getProductBasicDetailById(item.getProductId());
            if (detail.isInventoryTracked()) {
                list.add(get(unit, detail, item.getQuantity()));
            } else if (detail.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
                for (OrderItem addon : item.getComposition().getMenuProducts()) {
                    ProductBasicDetail addonProduct = productCache.getProductBasicDetailById(addon.getProductId());
                    if (addonProduct.isInventoryTracked()) {
                        list.add(get(unit, addonProduct, item.getQuantity()));
                    }
                }
            }
        }
        return list;
    }

    private ProductInventory get(UnitBasicDetail unit, ProductBasicDetail detail, int quantity) {
        ProductInventory product = new ProductInventory();
        product.setUnit(unit);
        product.setProduct(detail);
        product.setQuantity(quantity);
        return product;
    }

    private Boolean saveCustomerInfoBrandWise(Order order, CreateOrderResult result) {
        long orderBrandTime = System.currentTimeMillis();
        Boolean newBrandCustomer = false;
        boolean isSpecialOrder = false;
        for(OrderItem item : order.getOrders()){
            if(AppUtils.isGiftCard(item.getCode()) || Objects.nonNull(productCache.getSubscriptionProductDetail(item.getProductId()))){
                isSpecialOrder =true;
            }
        }
        try {
            if (order.getCustomerId() > 5) {
                newBrandCustomer = customerService.saveCustomerInfoBrandWise(order.getCustomerId(), order.getBrandId(),
                        result.getOrderId(), order.getBillStartTime(),isSpecialOrder);
                log.info("customer brand mapping details saved in db in {} milliseconds",
                        System.currentTimeMillis() - orderBrandTime);
            }
        } catch (Exception e) {
            log.error("error while saving customer {} brandwise info for brand {}", order.getCustomerId(),
                    order.getBrandId());
        }
        return newBrandCustomer;
    }

    @Override
    public void addDeliveryPartner(Integer orderId, Order order, OrderInfo info) {
        if (TransactionUtils.isCODOrder(order.getSource())) {
            try {
                Integer deliveryPartnerId = order.getDeliveryPartner();
                log.info("Creating delivery partner ticket  " + orderId);
                // in case none or pickup is not selected
                if (Objects.nonNull(deliveryPartnerId) && deliveryPartnerId != AppConstants.DELIVERY_PARTNER_NONE
                        && deliveryPartnerId != AppConstants.DELIVERY_PARTNER_PICKUP) {

                    order.setDeliveryPartner(1); // error handling for null
                    // value

                    if (deliveryPartnerId == AppConstants.DELIVERY_PARTNER_AUTOMATED) {
                        // adding delivery details to the order info object
                        info = deliveryRequestService.addDeliveryDetails(info,
                                unitCacheService.getUnitById(order.getUnitId()));
                    } else {
                        IdCodeName partnerObj = unitCacheService.getDeliveryPartnerById(deliveryPartnerId);
                        if (deliveryRequestService.checkIfAutomated(deliveryPartnerId)) {
                            // adding delivery details to the order info object
                            info = deliveryRequestService.addDeliveryDetails(info,
                                    unitCacheService.getUnitById(order.getUnitId()), partnerObj);
                        } else {
                            info.setDeliveryPartner(partnerObj);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Encountered error while creating delivery request :::::", e);
                order.setDeliveryPartner(1); // no delivery partner assigned
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateOfferCode(Order order, Integer orderId, OrderInfo info) {
        // updating coupon code applied for the order
        if (StringUtils.isNotBlank(order.getOfferCode())
                && !TransactionConstants.SIGNUP_OFFER_CODE.equals(order.getOfferCode())) {
            customerService.addOfferDetail(info.getCustomer().getId(), order.getOfferCode(), orderId);
            offerManagementService.updateCouponUsageByOne(info.getCustomer().getId(), order.getOfferCode(), orderId);
        } else if (StringUtils.isNotBlank(order.getOfferCode()) && (order.isContainsSignupOffer()
                || TransactionConstants.SIGNUP_OFFER_CODE.equals(order.getOfferCode()))) {
            customerService.addOfferDetail(info.getCustomer().getId(), TransactionConstants.SIGNUP_OFFER_CODE, orderId);
        }
        try {
            log.info("Updating Customer Campaign Offer Detail for orderId {}", info.getOrder().getOrderId());
            customerOfferManagementService.updateOfferApplicationDetails(order.getOfferCode(), order.getCustomerId(),
                    orderId, order.getTransactionDetail().getSavings());
        } catch (Exception e) {
            log.info("Error Updating Customer Campaign Offer Detail for orderId {}", info.getOrder().getOrderId(), e);
        }
    }

    @Override
    public void complimentryValidation(Order order) {
        // [WARNING] Removes customer if complimentary = SAMPLING
        if (TransactionUtils.isSpecialOrder(order)) {
            if (Objects.isNull(order.getCustomerId()) || (!TransactionUtils.isCODOrder(order.getSource())
                    && properties.getDummyCustomerId() != order.getCustomerId())) {
                order.setCustomerId(properties.getDummyCustomerId());
            }
        }
    }

    @Override
    public boolean hasGiftCard(Order order) {
        for (OrderItem item : order.getOrders()) {
            if (AppUtils.isGiftCard(item.getCode())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isSubscriptionPresent(Order order) {
        if (CollectionUtils.isEmpty(order.getOrders())) {
            return false;
        }
        for (OrderItem item : order.getOrders()) {
            if (productCache.getSubscriptionProductDetailsById(item.getProductId()).isPresent()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isRoutedToAssembly(Unit unit, Order order, boolean hasGiftcards, boolean hasSelectOrder) {
        if (UnitCategory.CHAI_MONK.equals(unit.getFamily()) || TransactionUtils.isBillBookOrder(order) || hasGiftcards
                || hasSelectOrder) {
            return false;
        }
        return TransactionUtils.isTakeawayOrder(order.getSource()) || TransactionUtils.isCODOrder(order.getSource())
                || TransactionUtils.isWebDineIn(order.getSource(), order.getChannelPartner())
                || unit.isWorkstationEnabled();
    }

    @Override
    public Order applyFreeItemOffer(boolean newCustomer, Order order) {
        Date businessDate = AppUtils.getBusinessDate();
        if (freeItemOfferService.hasFreeItemOffer(businessDate)
                && AppConstants.ORDER_TYPE_REGULAR.equals(order.getOrderType()) && order.getOfferCode() == null) {
            if (!hasGiftCardOrComboOrComplimentary(order)) {
                int productId = freeItemOfferService.freeItemOfferProductId(businessDate);
                boolean isInventoryEnabled = productCache.getProductBasicDetailById(productId).isInventoryTracked();
                try {
                    if (isInventoryEnabled) {
                        List<Integer> productIds = new ArrayList<>();
                        productIds.add(productId);
                        List<ProductInventory> inventory = inventoryService
                                .getUnitInventoryForProducts(order.getUnitId(), productIds);
                        if (inventory != null && inventory.size() == 1 && inventory.get(0).getQuantity() > 0) {
                            return freeItemOfferService.applyFreeItemOffer(businessDate, newCustomer, order);
                        }

                    } else {
                        return freeItemOfferService.applyFreeItemOffer(businessDate, newCustomer, order);
                    }
                } catch (DataNotFoundException e) {
                    log.error("Error in fetching inventory details for free item offer", e);
                }
            }
        }
        return order;
    }

    @Override
    public void validateGiftCardAndSubscription(Order order) throws CardValidationException {
        if (!CollectionUtils.isEmpty(order.getOrders())) {
            for (OrderItem item : order.getOrders()) {
                if (AppUtils.isGiftCard(item.getCode())) {
                    order.setGiftCardOrder(true);
                }
                if (order.isSubscriptionOrder()
                        || productCache.getSubscriptionProductDetailsById(item.getProductId()).isPresent()) {
                    order.setSubscriptionOrder(true);
                }
            }
            if (order.isGiftCardOrder() && Objects.nonNull(order.getCustomerId())
                    && AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())) {
                throw new CardValidationException(
                        "Order Cannot be punched for the Excluded Customer ::: " + order.getCustomerId());
            }
        }
    }

    private boolean hasGiftCardOrComboOrComplimentary(Order order) {
        // TODO similar handling as for gift card and subscription
        if (order.isGiftCardOrder()) {
            return true;
        }
        for (OrderItem i : order.getOrders()) {
            if (AppUtils.isCombo(i.getCode())) {
                return true;
            }
            if (i.getComplimentaryDetail() != null && i.getComplimentaryDetail().isIsComplimentary()) {
                return true;
            }
        }
        return false;
    }

    private OrderInfo createOrderInfo(Integer orderId, Order order, boolean includeReceipts, boolean newBrandCustomer,
                                      Pair<String, String> feedbackUrl, CreateOrderResult result, boolean newCustomer)
            throws DataNotFoundException, TemplateRenderingException {

        if (TransactionUtils.isBillBookOrder(order)) {
            includeReceipts = false;
        }
        long time = System.currentTimeMillis();
        OrderInfo info = generateOrderInfo(orderId, includeReceipts, order.getCustomerName(), order);
        log.info("Reciept took {}", System.currentTimeMillis() - time);
        info.getCustomer().setNewCustomerForBrand(newBrandCustomer);
        try {
            if (Objects.nonNull(feedbackUrl) && result.isGenerateQRCode()) {
                String qrReceiptContent = null;
                QRRawPrintReceipt assembly = new QRRawPrintReceipt(unitCacheService.getUnitById(order.getUnitId()),
                        info, properties.getBasePath(), TransactionConstants.QR_CODE_HEARDER, feedbackUrl.getSecond(),
                        properties);
                qrReceiptContent = assembly.getContent();
                if (info.getAndroidReceipts() != null && info.getAndroidReceipts().size() > 0
                        && qrReceiptContent != null) {
                    info.getAndroidReceipts().put(ReceiptType.QR_CODE, qrReceiptContent);
                }
                if (info.getReceipts() != null && info.getReceipts().size() > 0 && qrReceiptContent != null) {
                    info.getReceipts().add(qrReceiptContent);
                }
                info.setFeedbackUrl(feedbackUrl.getSecond());
                info.setQrCode(feedbackUrl.getSecond());
                info.getOrder().setQrLink(feedbackUrl.getSecond());
                info.getOrder().setQrHeader(TransactionConstants.QR_CODE_HEARDER);
            } else if (feedbackUrl != null && result.isGenerateInAppFeedback()) {
                info.setFeedbackUrl(feedbackUrl.getFirst());
            }
        } catch (Exception e) {
            log.error("Error in setting QR link", e);
//			new ErrorNotification("Error in setting QR link", "Error in setting QR link", e,
//					getEnvironmentProperties().getEnvironmentType()).sendEmail();
        }
        info.getOrder().setNewCustomer(newCustomer);
        info.setNewCards(result.getGiftCard());
        // Explicitly set consumables to orderInfo
        for (OrderItem item : order.getOrders()) {
            info.getOrder().getOrders().stream().filter(orderItem -> orderItem.getProductId() == item.getProductId())
                    .forEach(ele -> {
                        if (Objects.nonNull(item.getComposition())) {
                            if (Objects.nonNull(item.getComposition().getOthers())
                                    && !item.getComposition().getOthers().isEmpty()
                                    && (Objects.isNull(ele.getComposition().getOthers())
                                    || ele.getComposition().getOthers().isEmpty())) {
                                ele.getComposition().getOthers().addAll(item.getComposition().getOthers());
                                // LOG.info("Modified orderItem :::::: {}1", new Gson().toJson(ele));
                            }
                        }
                    });
        }
        // LOG.info("Modified orderinfo :::::: {}", new Gson().toJson(info));
        return info;
    }

    @Override
    public OrderInfo generateOrderInfo(int orderId, boolean includeReceipts, String customerName, Order orderData)
            throws DataNotFoundException, TemplateRenderingException {
        Order order = getOrderDetail(orderId);
        order.setCashCardExtraAmt(orderData.getCashCardExtraAmt());
        order.setCashCardPendingAmt(orderData.getCashCardPendingAmt());
        OrderInfo info = getOrderInfo(order, includeReceipts, customerName);
        if(Objects.nonNull(orderData.isSubscriptionOrder())){
            info.getOrder().setSubscriptionOrder(orderData.isSubscriptionOrder());
        }
        setDeliveryDetail(info);
        return info;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private Order getOrderDetail(int orderId) throws DataNotFoundException {
        try {
            Optional<OrderDetail> orderData = orderDetailDao.findById(orderId);// TODO object returned in create order?
            if (orderData.isEmpty()) {
                throw new DataNotFoundException();
            }
            OrderDetail data = orderData.get();
            SubscriptionPlan plan = subscriptionPlanDao.findByCustomerIdAndStatus(data.getCustomerId(),
                    AppConstants.ACTIVE);
            long time = System.currentTimeMillis();
            Order order = convertOrder(data, null);
            log.info("get order details in reciept took {} ", System.currentTimeMillis() - time);
            if (Objects.nonNull(plan) && Objects.nonNull(plan.getOverAllSaving())) {
                getSubscriptionObject(plan, order);
            }
            return order;
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Order Details with ID : %d", orderId), e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Order getOrderDetail(String generatedOrderId) throws DataNotFoundException {
        OrderDetail orderData = orderDetailDao.findByGeneratedOrderId(generatedOrderId);
        Order order = convertOrder(orderData, null);
        try {
            OrderRefundDetail orderRefundDetail = orderRefundDetailDao.findByOrderId(orderData.getOrderId());
            if (Objects.nonNull(orderRefundDetail)) {
                order.setOrderRefundDetailId(orderRefundDetail.getOrderRefundDetailId());
            }
        }
        catch (Exception e) {
            throw new DataNotFoundException(String.format("Did not find Order Details with ID : %s", generatedOrderId), e);
        }
        return order;
    }


    private Order convertOrder(OrderDetail order, OrderFetchStrategy strategy) {
        if ((strategy == null || (Objects.nonNull(strategy) && !strategy.isDonotFetchCustomerAddress()))
                && Objects.nonNull(order.getDeliveryAddress()) && !order.getDeliveryAddress().equals(0)) {
            Optional<CustomerAddressInfo> address = customerAddressInfoDao.findById(order.getDeliveryAddress());
            if (address.isPresent()) {
                CustomerAddressInfo info = address.get();
                if (StringUtils.isNotBlank(info.getName())) {
                    order.setCustomerName(
                            info.getName().substring(0, info.getName().length() > 45 ? 45 : info.getName().length()));
                }
            }
        }
//	                     OrderDetail orderDetail = dao.find(OrderDetail.class, orderId);
        if (Objects.nonNull(order.getInvoiceId())) {
            try {
                OrderInvoiceDetail orderInvoiceDetail = getInvoiceDetail(order.getGeneratedOrderId());
                order.setInvoiceDetail(orderInvoiceDetail);
            } catch (Exception e) {
                log.info("Unable to fetch invoice Id for this order");
            }
        }
        return orderConverter.convert(order, strategy);
    }

    private OrderInvoiceDetail getInvoiceDetail(String generatedOrderId) {
        OrderInvoiceDetail orderInvoiceDetail = orderInvoiceDetailDao.findByOrderId(generatedOrderId);
        log.info("***************************************{}", orderInvoiceDetail);
        return orderInvoiceDetail;
    }

    private void getSubscriptionObject(SubscriptionPlan plan, Order order) {
        Subscription subscription = new Subscription();
        subscription.setOverAllSavings(plan.getOverAllSaving());
        order.setSubscriptionDetail(subscription);
    }

    private Map<Integer,String> getCODItemNames(Integer orderId){
        Map<Integer,String> itemNameMap = new HashMap<>();
        try {
            List<OrderItemMetaDataDetail> itemList = orderItemMetaDataDetailDao.findByOrderId(orderId);
            itemNameMap = (Map<Integer, String>) itemList.stream()
                    .filter(item -> Objects.nonNull(item.getItemName()))
                    .collect(Collectors.toMap(
                            OrderItemMetaDataDetail::getOrderItemId,
                            OrderItemMetaDataDetail::getItemName));
        }catch (Exception e){
            log.error("Error While Finding COD Menu Name For Order Item");
        }
        return  itemNameMap;
    }

    @Override
    public OrderInfo getOrderInfo(Order order, boolean includeReceipts, String customerName) {
        long time = System.currentTimeMillis();
        Unit unit = unitCacheService.getUnitById(order.getUnitId());
        LoyaltyEvents loyaltyEvents = loyaltyEventsDao
                .findByOrderIdAndTransactionTypeAndTransactionCodeTypeAndTransactionStatus(order.getOrderId(),
                        TransactionType.DEBIT.name(), "Addition", LOYALTY_EVENT_SUCCESS_STATUS);
        if (Objects.isNull(loyaltyEvents)) {
            loyaltyEvents = new LoyaltyEvents();
            loyaltyEvents.setTransactionPoints(0);
        }
        Customer customer = customerService.getCustomer(order.getCustomerId());
        if (StringUtils.isBlank(customer.getEmailId())) {
            customer.setEmailId(properties.getUndeliveredEmail());
        }
        // Customer Name addition
        if (StringUtils.isNotBlank(customerName)) {
            order.setCustomerName(customerName);
        } else {
            order.setCustomerName(customer.getId() > 5 ? customer.getFirstName() : null);
        }
        // Customer address clear out
        Address a = null;
        if (order.getDeliveryAddress() != null) {
            for (Address address : customer.getAddresses()) {
                if (order.getDeliveryAddress().equals(address.getId())) {
                    a = address;
                }
            }
        }
        if (!CollectionUtils.isEmpty(customer.getAddresses())) {
            customer.getAddresses().clear();
        }
        if (a != null) {
            customer.getAddresses().add(a);
            if (a.getName() != null) {
                order.setCustomerName(a.getName());
            }
        }

        OrderInfo info = new OrderInfo(properties.getEnvironmentType(), order, customer, unit,
                unitCacheService.getDeliveryPartnerById(order.getDeliveryPartner()),
                unitCacheService.getChannelPartnerById(order.getChannelPartner()));
        if(Objects.nonNull(order.getSource()) && UnitCategory.COD.name().equals(order.getSource())) {
            Optional<PartnerOrderRiderStatesDetail> partnerOrderRiderStatesDetailOptional = partnerOrderRiderStatesDetailDao.findBykettleOrderId(order.getOrderId());
            partnerOrderRiderStatesDetailOptional.ifPresent(partnerOrderRiderStatesDetail -> info.setPartnerOrderRiderStates(partnerOrderRiderStatesDetailMapper.mapToDetailData(partnerOrderRiderStatesDetail)));
            Map<Integer, String> itemMenuNameMap = getCODItemNames(order.getOrderId());
            if (!CollectionUtils.isEmpty(itemMenuNameMap)) {
                setItemMenuNames(itemMenuNameMap, info.getOrder());
            }
            if (properties.isPriortizationOfOrdersEnabled()) {
                info.getOrder().setPrioritizedOrder(isPrioritizedOrder(info.getOrder().getOrderId()));
            }
        }
        info.setBrand(brandMetaDataCache.getBrandMetaData().get(order.getBrandId()));
        info.getOrder().setEarnedLoyaltypoints(loyaltyEvents.getTransactionPoints());
        info.setCashCardPendingAmt(order.getCashCardPendingAmt());
        info.setCashCardExtraAmt(order.getCashCardExtraAmt());
        info.setCashCardPrevAmt(order.getCashCardPrevAmt());
        if (Objects.nonNull(order.getInvoice())) {
            info.setOrderInvoice(order.getInvoice());
        }
        try{
            OrderStatusEvent orderStatusEvent = orderStatusEventDao.findTop1ByOrderIdOrderByOrderIdDesc(order.getOrderId());
            if(Objects.nonNull(orderStatusEvent)){
                info.setLastOrderStatusEventTime(orderStatusEvent.getUpdateTime());
            }
        }catch (Exception e){
            log.info("Unable to setLastOrderStatusEventTime for order {}",order.getGenerateOrderId());
        }
        return info;
    }

    private void setItemMenuNames(Map<Integer,String> itemMenuNameMap , Order order){
        order.getOrders().forEach(orderItem -> {
            orderItem.setItemName(itemMenuNameMap.get(orderItem.getItemId()));
        });
    }

    @Override
    public Boolean isPrioritizedOrder(Integer kettleOrderId){
        try {
            String status = partnerOrderDetailDao.findIsPrioritizedByKettleOrderId(kettleOrderId);
            return AppUtils.getStatus(status);
        }catch (Exception e){
            log.error("Error while Getting Partner Order Detail for kettle order id : {} ",kettleOrderId);
            return false;
        }
    }

    @Override
    public OrderInfo generateReciept(OrderInfo info) {
        long time = System.currentTimeMillis();
        /*
         * boolean needsCafeOrderPrint = info.getOrder().getChannelPartner() ==
         * AppConstants.CHANNEL_PARTNER_WEB_APP || info.getUnit().isTableService() ||
         * info.getCustomer() == null || info.getUnit().isTokenEnabled() ||
         * info.getCustomer().getEmailId() == null ||
         * info.getCustomer().getEmailId().trim().length() == 0 ||
         * props.getUndeliveredEmail().equals(info.getCustomer().getEmailId( ));
         */
        Order order = info.getOrder();
        boolean needsCafeOrderPrint = !TransactionUtils.isSpecialOrder(order)
                || TransactionUtils.isPaidEmployeeMeal(order);

        Map<ReceiptType, String> androidReceipts;
        try {
            androidReceipts = TransactionUtils.getReceipts(properties.getChaayosBaseUrl(), needsCafeOrderPrint, info,
                    properties.getBasePath(), properties.getRawPrintingSatus(), properties);
            info.setAndroidReceipts(androidReceipts);
            List<String> receipts = new ArrayList<>();
            for (ReceiptType r : androidReceipts.keySet()) {
                receipts.add(androidReceipts.get(r));
            }
            info.setReceipts(receipts);
        } catch (TemplateRenderingException e) {
            log.error("Unable to render reciept Template", e);
        } catch (DataNotFoundException e) {
            log.error("exception occured while generating reciept", e);
        }
        if (properties.getRawPrintingSatus()) {
            info.setPrintType(PrintType.RAW);
        } else {
            info.setPrintType(PrintType.HTML);
        }

        log.info("get order info in reciept took {} ", System.currentTimeMillis() - time);
        return info;
    }

    @Override
    public void setDeliveryDetail(OrderInfo orderInfo) {
        if (orderInfo != null) {
            DeliveryDetail deliveryDetails = null;
            List<DeliveryDetail> details = deliveryDetailDao
                    .findByGeneratedOrderIdAndDeliveryStatusOrderByStatusUpdateTimeDesc(
                            orderInfo.getOrder().getGenerateOrderId(), AppConstants.CANCELLED);
            if (!CollectionUtils.isEmpty(details)) {
                deliveryDetails = details.get(0);
            }
            orderInfo.setDeliveryDetails(
                    deliveryDetailConverter.convert(orderInfo.getOrder().getUnitId(), deliveryDetails));
            if (deliveryDetails != null) {
                orderInfo.setDeliveryPartner(
                        unitCacheService.getDeliveryPartnerById(deliveryDetails.getDeliveryPartnerId()));
            }
        }
    }

    @Override
    public Order extractWalletOrder(Order order, WalletOrderType type, WalletOrder wallet) {
        Order walletOrder = orderConverter.extractWalletOrder(order, type, wallet);
//        if(type.equals(WalletOrderType.MICRO)){
//            List<CashCardDetail> cashCards = cardService.getActiveCashCards(walletOrder.getCustomerId());
//            BigDecimal pendingAmt = BigDecimal.ZERO;
//            for (CashCardDetail cardDetail : cashCards) {
//                pendingAmt = pendingAmt.add(cardDetail.getCashPendingAmount());
//            }
//            walletOrder.setCashCardPrevAmt(pendingAmt);
//        }
        return walletOrder;
    }

    public Order validateOrder(OrderDomain orderDetail, OrderUnitMapping orderUnitMapping)
            throws DataUpdationException {
        if (!UnitStatus.ACTIVE.toString().equals(AppConstants.ACTIVE)) {
            throw new DataUpdationException("Unit is IN_ACTIVE : Please activate unit to place orders");
        }
        Order order = orderDetail.getOrder();
        Object mutex = keyMutexFactory
                .getMutex(order.getSource() + order.getUnitId() + order.getTerminalId() + order.getEmployeeId());
        orderUnitMapping.setOrderSource(order.getSource());
        orderUnitMapping.setUnitId(order.getUnitId());
        orderUnitMapping.setTerminalId(order.getTerminalId());
        orderUnitMapping.setEmployeeId(order.getEmployeeId());
        synchronized (mutex) {
            if (order.getSubscriptionDetail() == null) {
                    orderMappingCache.add(orderUnitMapping, order.getGenerateOrderId());
                    order.setGenerateOrderId(null);
            }

        }
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo processOrder(OrderDomain orderDetail) throws DataNotFoundException, TemplateRenderingException,
            CardValidationException, DataUpdationException, JMSException, OfferValidationException {
        String randomString = orderDetail.getOrder().getGenerateOrderId();
        OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
        if(Objects.nonNull(orderDetail.getOrder())){
            for(OrderItem orderItem:orderDetail.getOrder().getOrders()){
                Product product = productCache.getProductById(orderItem.getProductId());
                if(Objects.nonNull(product)){
                    orderItem.setProductName(product.getName());
                }
            }
        }

        if(Objects.nonNull(orderDetail.getOrder().getDreamFolksVoucherDetails()) && Objects.nonNull(orderDetail.getOrder().getDreamFolksVoucherDetails().getVoucherCode())
           && dreamFolksVoucherUsageCache.getDreamFolksVoucherCodesUsed().contains(orderDetail.getOrder().getDreamFolksVoucherDetails().getVoucherCode())){
            throw new CardValidationException("DreamFolks Voucher Code Already Used");
        }

        if(!revalidatingCreateOrder(orderDetail.getOrder())){
            throw new CardValidationException("Order Validation Failed");
        }
        Order order = validateOrder(orderDetail, orderUnitMapping);
        WalletOrder wallet = orderDetail.getWalletOrder();
        if (validateWalletOrder(wallet, order)) {
            return createWalletOrder(order, wallet, orderDetail, randomString, orderUnitMapping);
        } else {
            long time = System.currentTimeMillis();
            OrderInfo info = processPlainOrder(order, orderDetail.isIncludeReceipts(), orderDetail.isAddMetadata(),
                    orderDetail.getOrderNotification(), randomString, orderUnitMapping);
            if (orderDetail.isIncludeReceipts()) {
                if((Objects.nonNull(info.getCashCardPendingAmt()) && info.getCashCardPendingAmt().compareTo(BigDecimal.ZERO)==0) || Objects.isNull(info.getCashCardPendingAmt())){
                    info.setCashCardPendingAmt(orderDetail.getOrder().getCurrentWalletAmount());
                }
                OrderInfo receiptInfo = generateReciept(info);
                info.setReceipts(receiptInfo.getReceipts());
                info.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
                info.setAndroidReceipts(receiptInfo.getAndroidReceipts());
            }
            log.info("Full Order Processing Time for {} :: {} millisecond ", info.getOrder().getGenerateOrderId(),
                    System.currentTimeMillis() - time);
			if (MapUtils.isEmpty(info.getOrderNotificationMap())) {
				info.setOrderNotificationMap(new HashMap<>());
			}
			info.getOrderNotificationMap().put(info.getOrder().getOrderId(), info.getOrderNotification());
			return info;
		}
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo processLoyaltyWalletOrder(String contactNumber,Integer loyaltyPoints,Integer walletAmount,Integer extraAmount){
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try{
            CustomerInfo customerInfo = customerService.getCustomerInfo(contactNumber);
            List<LoyaltyEvents> loyaltyEventsList = loyaltyEventsDao.
                    findAllByCustomerIdAndTransactionCodeAndTransactionStatusOrderByTransactionTimeDesc(customerInfo.getCustomerId()
                            ,LoyaltyEventType.WALLET_RECHARGE.name(),LOYALTY_EVENT_SUCCESS_STATUS);
            Integer daysDiff =8;
            if(Objects.nonNull(loyaltyEventsList) && !loyaltyEventsList.isEmpty()){
                daysDiff = AppUtils.getAbsDaysDiff(loyaltyEventsList.get(0).getTransactionTime(),AppUtils.getCurrentTimestamp());
            }
            LoyaltyScore loyaltyScore = loyaltyService.getScore(customerInfo.getCustomerId());
            Integer score = loyaltyScore.getAcquiredPoints();
            boolean isEligible = loyaltyService.validateLoyaltyPoint(loyaltyPoints, loyaltyScore);
            if(isEligible && daysDiff>7){
                OrderDomain orderDomain = orderConverter.getOrderDomain(customerInfo,walletAmount,extraAmount);
                try {
                    OrderInfo orderInfo = processOrder(orderDomain);
                    try {
                        boolean update = loyaltyService.createLoyaltyEventAndUpdateLoyaltyScore(loyaltyPoints, customerInfo.getCustomerId(), contactNumber, LoyaltyEventType.WALLET_RECHARGE,
                                loyaltyScore, orderInfo.getOrder().getOrderId());
                        if(!update){
                            log.info("Failed to update loyalty");
                        }else {
                            return orderInfo;
                        }
                    }catch (Exception e){
                        log.error("Error in update customer loyalty");
                    }
                }catch (Exception e){
                    log.error("Error in placing Order", e);
                }
            }else {
                log.info("cusotmer don't have enough loyalty points or doing transaction within 7 days");
            }
        }catch (Exception e){
            log.error("Customer is not a Chaayos customer",e);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo createWalletOrder(Order order, WalletOrder wallet, OrderDomain orderDetail, String randomString,
                                       OrderUnitMapping orderUnitMapping) throws OfferValidationException, CardValidationException,
            DataNotFoundException, DataUpdationException, TemplateRenderingException, JMSException {
        if (CollectionUtils.isEmpty(order.getOrders())) {
            Order walletOrder = extractWalletOrder(order, WalletOrderType.DIRECT, wallet);
            return createDirectWalletOrder(walletOrder, wallet, orderDetail, randomString, orderUnitMapping);
        } else {
            long time = System.currentTimeMillis();
            Order walletOrder = extractWalletOrder(order, WalletOrderType.MICRO, wallet);
            OrderInfo data = createMicroWalletOrder(walletOrder, order, orderDetail, randomString, orderUnitMapping,
                    wallet.isDeductFromWallet());
            log.info("Full Order Processing Time for {} :: {} millisecond ", data.getOrder().getGenerateOrderId(),
                    System.currentTimeMillis() - time);
            return data;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo createDirectWalletOrder(Order order, WalletOrder wallet, OrderDomain orderDetail,
                                             String randomString, OrderUnitMapping orderUnitMapping) throws DataNotFoundException, DataUpdationException,
            CardValidationException, TemplateRenderingException, JMSException {
        long time = System.currentTimeMillis();
        OrderInfo info = processPlainOrder(order, orderDetail.isIncludeReceipts(), orderDetail.isAddMetadata(),
                orderDetail.getOrderNotification(), randomString, orderUnitMapping);
        addwalletForReciept(info, null, WalletOrderType.DIRECT);
        info.setCashCardExtraAmt(
                (Objects.nonNull(wallet.getExtraAmount()) && wallet.getExtraAmount().compareTo(BigDecimal.ZERO) != 0)
                        ? wallet.getExtraAmount()
                        : wallet.getSuggestedAmount());
        info.setCashCardPurchaseAmt((Objects.nonNull(wallet.getRechargeAmount())
                && wallet.getRechargeAmount().compareTo(BigDecimal.ZERO) != 0) ? wallet.getRechargeAmount()
                : wallet.getSuggestedAmount());
        if (orderDetail.isIncludeReceipts()) {
            OrderInfo receiptInfo = generateReciept(info);
            info.setReceipts(receiptInfo.getReceipts());
            info.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
            info.setAndroidReceipts(receiptInfo.getAndroidReceipts());
        }
        if (MapUtils.isEmpty(info.getOrderNotificationMap())) {
            info.setOrderNotificationMap(new HashMap<>());
        }
        info.getOrderNotificationMap().put(info.getOrder().getOrderId(), info.getOrderNotification());
        log.info("Full Order Processing Time for {} :: {} millisecond ", info.getOrder().getGenerateOrderId(),
                System.currentTimeMillis() - time);
        return info;
    }

    @Override
    public OrderInfo createMicroWalletOrder(Order walletOrder, Order plainOrder, OrderDomain orderDetail,
                                            String randomString, OrderUnitMapping orderUnitMapping, boolean deductFromWallet)
            throws DataNotFoundException, DataUpdationException, CardValidationException, TemplateRenderingException,
            JMSException {
        long time = System.currentTimeMillis();
        OrderInfo info = null;
        info = processPlainOrder(walletOrder, orderDetail.isIncludeReceipts(), orderDetail.isAddMetadata(),
                orderDetail.getOrderNotification(), randomString, orderUnitMapping);
        plainOrder.setRefOrderId(info.getOrder().getOrderId());
        setForceLoyalty(plainOrder, info, null);
        OrderInfo orderInfo = processPlainOrder(plainOrder, orderDetail.isIncludeReceipts(),
                orderDetail.isAddMetadata(), orderDetail.getOrderNotification(), randomString, orderUnitMapping);
        addwalletForReciept(orderInfo, info, WalletOrderType.MICRO);
        orderInfo.getRefrenceOrderIds().add(info.getOrder().getOrderId());
        if (orderDetail.isIncludeReceipts()) {
            OrderInfo receiptInfo = generateReciept(orderInfo);
            orderInfo.setReceipts(receiptInfo.getReceipts());
            orderInfo.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
            orderInfo.setAndroidReceipts(receiptInfo.getAndroidReceipts());
        }
        log.info("Full Order Processing Time for {} :: {} millisecond ", orderInfo.getOrder().getGenerateOrderId(),
                System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        if (MapUtils.isEmpty(orderInfo.getOrderNotificationMap())) {
            orderInfo.setOrderNotificationMap(new HashMap<>());
        }
        orderInfo.getOrderNotificationMap().put(orderInfo.getOrder().getOrderId(), orderInfo.getOrderNotification());
        orderInfo.getOrderNotificationMap().put(info.getOrder().getOrderId(), info.getOrderNotification());
        return orderInfo;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private CreateOrderResult placeOrder(Order order, StringBuilder sb) throws DataUpdationException {
        CreateOrderResult result = null;
        if (TransactionUtils.isCODOrder(order.getSource())) {
            order.setGenerateOrderId(AppUtils.generateRandomOrderId());
            result = createOrder(order, sb);
        } else {
            UnitSessionDetail session = unitSessionCache
                    .get(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
            order.setGenerateOrderId(session.getGeneratedOrderId());
            if (Objects.isNull(order.getCustomerId())) {
                order.setCustomerId(Objects.isNull(session.getCustomer()) ? properties.getDummyCustomerId()
                        : session.getCustomer().getId());
            }
            result = createOrder(order, sb);
            if (!CollectionUtils.isEmpty(order.getEnquiryItems())) {
                enquiryItemService.addOrderEnquiryItems(order, result.getOrderId());
            }
            unitSessionCache.generateToken(new UnitTerminalDetail(session.getUnitId(), session.getTerminalId()));
        }
        return result;
    }

    private void setTransactionDetail(OrderDetail detail, TransactionDetail transaction,BigDecimal serviceTax) {
        if (transaction.getDiscountDetail() != null) {
            if (transaction.getDiscountDetail().getDiscount() != null) {
                detail.setDiscountAmount(transaction.getDiscountDetail().getDiscount().getValue());
                detail.setDiscountPercent(transaction.getDiscountDetail().getDiscount().getPercentage());
            } else {
                detail.setDiscountAmount(BigDecimal.ZERO);
                detail.setDiscountPercent(BigDecimal.ZERO);
            }
            if (transaction.getDiscountDetail().getPromotionalOffer() != null) {
                detail.setPromotionalDiscount(transaction.getDiscountDetail().getPromotionalOffer());
            } else {
                detail.setPromotionalDiscount(BigDecimal.ZERO);
            }
            detail.setDiscountReason(transaction.getDiscountDetail().getDiscountReason());
            detail.setDiscountReasonId(transaction.getDiscountDetail().getDiscountCode());
        }
        if (transaction.getDiscountDetail() != null) {
            detail.setTotalDiscount(transaction.getDiscountDetail().getTotalDiscount()); // total
        } else {
            detail.setTotalDiscount(BigDecimal.ZERO); // total
        }

        detail.setRoundOffAmount(transaction.getRoundOffValue());

        detail.setTaxableAmount(transaction.getTaxableAmount());
        detail.setSettledAmount(transaction.getPaidAmount());
        if (Objects.nonNull(transaction.getCollectionAmount())) {
            detail.setCollectionAmount(transaction.getCollectionAmount());
        } else {
            detail.setCollectionAmount(transaction.getPaidAmount());
        }
        detail.setTotalAmount(transaction.getTotalAmount());
        detail.setSaleAmount(transaction.getTotalAmount().subtract(
                transaction.getDiscountDetail() != null && transaction.getDiscountDetail().getPromotionalOffer() != null
                        ? transaction.getDiscountDetail().getPromotionalOffer()
                        : new BigDecimal(0.0D)));
        detail.setSavingAmount(transaction.getSavings());
        detail.setTaxAmount(transaction.getTax());
        if(Objects.nonNull(transaction.getServiceCharge()) && BigDecimal.ZERO.compareTo(transaction.getServiceCharge()) != 0 ){
            if(!AppUtils.isDineInOrAppOrder(detail.getChannelPartnerId())) {
                detail.setTotalAmount(transaction.getTotalAmount().add(transaction.getServiceCharge()));
            }
            detail.setServiceCharge(transaction.getServiceCharge());
            detail.setServiceChargePercent(transaction.getServiceChargePercent());
            detail.setServiceTaxAmount(serviceTax);
        }
    }

    @Override
    public boolean isSubscriptionOrder(Order order) {
        return order.getSubscriptionDetail() != null && order.getSubscriptionDetail().getSubscriptionId() > 0;
    }

    public Integer getMappedVariant(OrderItem item) {
        int identifierProduct = DesiChaiConsumptionHelper.placeholderIdentifier(item.getProductId());
        List<IngredientVariantDetail> variants = new ArrayList<>();
        Integer productId = null;
        if (item.getComposition() != null && item.getComposition().getVariants() != null) {
            for (IngredientVariantDetail v : item.getComposition().getVariants()) {

                if (identifierProduct == v.getId()) {
                    productId = DesiChaiConsumptionHelper.getActualProduct(item.getProductId(), v.getAlias());
                } else {
                    variants.add(v);
                }
            }
            item.getComposition().setVariants(variants);
        }
        return productId;
    }

    private void setComboComplimentaryDetails(com.stpl.tech.kettle.data.kettle.OrderItem info) {
        info.setComplimentaryTypeId(AppConstants.COMPLEMENTARY_CODE_COMBO);
        info.setIsComplimentary(AppConstants.YES);
    }

    public void setComplimentaryDetails(com.stpl.tech.kettle.data.kettle.OrderItem info, OrderItem item, boolean isComboConsituent) {
        if (isComboConsituent) {
            setComboComplimentaryDetails(info);
            return;
        }
        if (item.getComplimentaryDetail() == null) {
            return;
        }
        info.setComplimentaryReason(item.getComplimentaryDetail().getReason());
        info.setComplimentaryTypeId(item.getComplimentaryDetail().getReasonCode());
        info.setIsComplimentary(AppConstants.getValue(item.getComplimentaryDetail().isIsComplimentary()));
    }

    public void setDiscount(com.stpl.tech.kettle.data.kettle.OrderItem info, OrderItem item) {
        if (item.getDiscountDetail() == null || item.getDiscountDetail().getDiscount() == null) {
            info.setDiscountAmount(BigDecimal.ZERO);
            info.setDiscountPercent(BigDecimal.ZERO);
            info.setPromotionalDiscount(BigDecimal.ZERO);
            return;
        }
        info.setDiscountAmount(item.getDiscountDetail().getDiscount().getValue());
        info.setDiscountPercent(item.getDiscountDetail().getDiscount().getPercentage());
    }

    private void setData(OrderDetail order, com.stpl.tech.kettle.data.kettle.OrderItem info, OrderItem item, boolean isComboConsituent,
                         com.stpl.tech.kettle.data.kettle.OrderItem parent) {
        int productId = item.getProductId();
        String productName = item.getProductName();
        if (DesiChaiConsumptionHelper.isPlaceholderProduct(productId)) {
            Integer pid = getMappedVariant(item);
            productId = pid == null ? productId : pid;
            productName = productCache.getProductBasicDetailById(productId).getDetail().getName();
        }
        setComplimentaryDetails(info, item, isComboConsituent);
        info.setOrderItemRemark(item.getOrderItemRemark());
        info.setBillType(item.getBillType() == null ? null : item.getBillType().name());
        info.setTaxCode(item.getCode());
        info.setDimension(item.getDimension());
        if (item.getDiscountDetail() != null) {
            setDiscount(info, item);
            info.setDiscountReason(item.getDiscountDetail().getDiscountReason());
            info.setDiscountReasonId(item.getDiscountDetail().getDiscountCode());
            if (item.getDiscountDetail().getPromotionalOffer() != null) {
                info.setPromotionalDiscount(item.getDiscountDetail().getPromotionalOffer());
            } else {
                info.setPromotionalDiscount(BigDecimal.ZERO);
            }
        }

        info.setHasAddon(AppConstants.getValue(item.getComposition() != null
                && item.getComposition().getAddons() != null && item.getComposition().getAddons().size() > 0));
        info.setPrice(item.getPrice() == null ? BigDecimal.ZERO : item.getPrice());
        info.setOriginalPrice(item.getOriginalPrice() == null ? item.getPrice() : item.getOriginalPrice());
        info.setProductId(productId);
        info.setParentItemId(parent != null ? parent.getOrderItemId() : null);
        info.setProductName(productName);
        info.setProductAliasName(Objects.nonNull(item.getProductAliasName()) ? item.getProductAliasName() : productName);
        if(isComboConsituent) {
            if(ComboQunantityStrategy.MULTIPLICATION.equals(item.getComboQuantityStrategy())) {
                info.setQuantity(parent != null ? item.getQuantity() * parent.getQuantity() : item.getQuantity());
            }
            else{
                info.setQuantity(item.getQuantity());
            }
        }else {
            info.setQuantity(parent != null ? parent.getQuantity() : item.getQuantity());
        }
        info.setTotalAmount(item.getPrice() == null ? BigDecimal.ZERO
                : item.getPrice().multiply(new BigDecimal(info.getQuantity())));
        if (isComboConsituent) {
            info.setPaidAmount(BigDecimal.ZERO);
        } else {
            info.setPaidAmount(item.getAmount() == null ? BigDecimal.ZERO : item.getAmount());
        }
        info.setComboConstituent(AppConstants.getValue(isComboConsituent));
        info.setOrderDetail(order);
        if (AppConstants.CHAAYOS_BRAND_ID == order.getBrandId()) {
            Integer recipeId = null;
            try {
                recipeId = recipeCache.getUnitProductRecipeId(order.getUnitId(), productId, item.getDimension());
            } catch (Exception e) {
                // Do Nothing
            }
            if (recipeId != null && recipeId != item.getRecipeId()) {
                info.setRecipeId(recipeId);
                item.setRecipeId(recipeId);
            } else {
                info.setRecipeId(item.getRecipeId());
            }
        } else {
            info.setRecipeId(item.getRecipeId());
        }
        String profile = null;
        try {
            profile = recipeCache.getRecipeByRecipeId(info.getRecipeId()).getProfile();
        } catch (Exception e) {
            profile = AppConstants.DEFAULT_RECIPE_PROFILE;
        }
        info.setRecipeProfile(profile);
        info.setTaxAmount(item.getTax());
        boolean takeAway = order.getChannelPartnerId() == 9 || (item.getTakeAway() != null && item.getTakeAway());
        info.setTakeAway(AppConstants.getValue(takeAway));
//recom category is added here which has index, and tags,
        info.setRecomCategory(item.getRecomCategory());
        info.setTaxDeductedByPartner(AppUtils.setStatus(Boolean.TRUE.equals(item.getTaxDeductedByPartner())));

//Added here the category and sub category
        info.setSourceCategory(item.getSourceCategory());
        info.setSourceSubCategory(item.getSourceSubCategory());
        info.setIsHoldOn(item.getIsHoldOn());

    }

    public String getUniqueCashCardNumber() {

        String cardNumber = AppUtils.generateRandomAlphaNumericCode(8);
        List<CashCardDetail> list = cashCardDetailDao.findAllByCardNumber(cardNumber);
        if (list.size() > 0) {
            getUniqueCashCardNumber();
        }
        return cardNumber;
    }

    private BigDecimal processCashCardPayment(Order order) throws CardValidationException {
        BigDecimal pendingAmount = null;
        BigDecimal deductedAmount = BigDecimal.ZERO;
        boolean amountReached =false;
        for (Settlement settlement : order.getSettlements()) {
            if (settlement.getMode() == properties.getCashCardPaymentModeId()) {
                if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
                        || Objects.isNull(order.getCustomerId())) {
                    throw new CardValidationException("Cash Card Not Valid for customerId " + order.getCustomerId());
                }
                if (Objects.isNull(pendingAmount)) {
                    pendingAmount = BigDecimal.ZERO;
                }
                settlement.setExternalSettlements(new ArrayList());
                BigDecimal amount = settlement.getAmount();
                deductedAmount = deductedAmount.add(amount);
                pendingAmount = pendingAmount.subtract(amount);
                List<CashCardDetail> cashCards = cashCardDetailDao.findAvailableCashCards(order.getCustomerId(),
                        AppConstants.ACTIVE);
                BigDecimal prevAmt = BigDecimal.ZERO;
                for (int i =0 ;i <cashCards.size() ;i++) {
                    CashCardDetail cashCard = cashCards.get(i);
                    prevAmt = prevAmt.add(cashCard.getCashPendingAmount());
                    if (amount.compareTo(BigDecimal.ZERO) == 1) {
                        BigDecimal value = cashCard.getCashPendingAmount().compareTo(amount) >= 0 ? amount
                                : cashCard.getCashPendingAmount();
                        settlement.getExternalSettlements()
                                .add(new ExternalSettlement(0, value, cashCard.getCashCardId().toString()));
                        amount = amount.subtract(value);
                    }else if(amount.compareTo(BigDecimal.ZERO) == 0){
                        if(!amountReached){
                            settlement.getExternalSettlements()
                                    .add(new ExternalSettlement(0, BigDecimal.ZERO, cashCard.getCashCardId().toString()));
                            amountReached = true;
                        }
                    }
                    pendingAmount = pendingAmount.add(cashCard.getCashPendingAmount());
                }
                order.setCashCardPrevAmt(prevAmt);
                if (amount.compareTo(BigDecimal.ZERO) != 0) {
                    throw new CardValidationException("Insufficient Cash Card for customerId " + order.getCustomerId());
                }
            }
        }
        order.setCashCardPendingAmt(pendingAmount);
        return deductedAmount;
    }

    private void addNewCardEntry(String cardNumber, OrderItem item) {
        try {
            CashCardDetail detail = new CashCardDetail();
            detail.setCardNumber(cardNumber);
            detail.setCardSerial(cardNumber);
            detail.setSerialNumber(cardNumber);
            detail.setCreationTime(AppUtils.getCurrentTimestamp());
            detail.setCardStatus(CashCardStatus.INITIATED.name());
            detail.setStartDate(AppUtils.getCurrentDate());
            if (StringUtils.isNotBlank(item.getCardType()) && CashCardType.GYFTR.name().equals(item.getCardType())) {
                detail.setEndDate(item.getValidUpto());
            } else {
                detail.setEndDate(AppUtils.getInfiniteDate());
            }
            if (item.getCardType().equalsIgnoreCase(CashCardType.AP01.name())) {
                detail.setCashInitialAmount(item.getTotalAmount());
            } else if (item.getCardType().equalsIgnoreCase(CashCardType.ECARD.name())) {
                detail.setCashInitialAmount(item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            } else {
                detail.setCashInitialAmount(item.getPrice());
            }
            detail.setCashPendingAmount(BigDecimal.ZERO);
            detail.setCardType(item.getCardType());
            cashCardDetailDao.save(detail);
        } catch (Exception e) {
            log.info("Error while creating system generated E-card for");
        }
    }

    private void updateCashCardDetails(OrderDetail orderDetail, OrderItem item, boolean suggestWalletGiftCard)
            throws DataUpdationException {
//				LOG.info("Item is qwertyuio {}",item);
        CashCardOffer offer = cashCardOfferDao.getCashCardOffer(item.getPrice(), orderDetail.getUnitId(),
                AppUtils.getCurrentBusinessDate(), AppConstants.ACTIVE);
        if (((item.getCardType().equalsIgnoreCase(CashCardType.ECARD.name())
                || item.getCardType().equalsIgnoreCase(CashCardType.AP01.name())) && item.getItemCode() == null)
                || voucherManagementService.isGyftrCard(item.getCardType())) {
            String cardNumber = getUniqueCashCardNumber();
            item.setItemCode(cardNumber);
            addNewCardEntry(cardNumber, item);
        }
        CashCardDetail cashCardDetail = cashCardDetailDao.findByCardNumber(item.getItemCode());
        cashCardDetail.setBuyerId(orderDetail.getCustomerId());
        cashCardDetail.setCardStatus(CashCardStatus.READY_FOR_ACTIVATION.name());
        cashCardDetail.setPurchaseTime(AppUtils.getCurrentTimestamp());
        cashCardDetail.setLastModified(AppUtils.getCurrentTimestamp());
        cashCardDetail.setPurchaseOrderId(orderDetail.getOrderId());
        if (item.getProductId() == AppConstants.PSEUDO_GIFT_CART_ID
                || (CashCardType.ECARD.name().equals(item.getCardType()) && offer == null)
                || CashCardType.AP01.name().equalsIgnoreCase(item.getCardType())) {
            BigDecimal offerAmount = item.getOfferAmount();
            cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
            cashCardDetail.setInitialOffer(offerAmount);
        } else {
            if (!CashCardType.GYFTR.name().equals(item.getCardType())
                    && !CashCardType.AP01.name().equalsIgnoreCase(item.getCardType()) && offer != null) {
                BigDecimal giftWalletOffer = Objects.nonNull(offer.getPercentage()) ? offer.getPercentage()
                        : BigDecimal.ZERO;
                BigDecimal suggestWalletOffer = Objects.nonNull(offer.getSuggestWalletPercentage())
                        ? offer.getSuggestWalletPercentage()
                        : BigDecimal.ZERO;
                BigDecimal offerAmount = suggestWalletGiftCard
                        ? AppUtils.percentOf(suggestWalletOffer, cashCardDetail.getCashInitialAmount())
                        : AppUtils.percentOf(giftWalletOffer, cashCardDetail.getCashInitialAmount());
                cashCardDetail.setCashPendingAmount(AppUtils.add(cashCardDetail.getCashInitialAmount(), offerAmount));
                cashCardDetail.setOfferId(offer.getCashCardOfferId());
                cashCardDetail.setInitialOffer(offerAmount);
            } else {
                cashCardDetail.setCashPendingAmount(cashCardDetail.getCashInitialAmount());
                cashCardDetail.setInitialOffer(BigDecimal.ZERO);
            }
        }
        cashCardDetailDao.save(cashCardDetail);

        if (!item.getCardType().equalsIgnoreCase(CashCardType.GIFT.name())) {
            activateCashCard(cashCardDetail, orderDetail);
        }

    }

    public void logCashCardEvent(CashCardDetail cashCardDetail, CashCardEventStatus cashCardEventStatus,
                                 GiftCardActivationRequest giftCardActivationRequest) throws DataUpdationException {
        CashCardEventsLogData cashCardEventsLogData = new CashCardEventsLogData();
        cashCardEventsLogData.setEventTime(AppUtils.getCurrentTimestamp());
        cashCardEventsLogData.setCardNumber(cashCardDetail.getCardNumber());
        cashCardEventsLogData.setCardSerial(cashCardDetail.getCardSerial());
        cashCardEventsLogData.setEvent(cashCardEventStatus.value());
        cashCardEventsLogData.setEventDetail(giftCardActivationRequest.getReason());
        cashCardEventsLogData.setEmpId(giftCardActivationRequest.getRequestingEmployee());
        cashCardEventsLogData.setUnitId(giftCardActivationRequest.getUnitId());
        cardEventsLogDataDao.save(cashCardEventsLogData);
    }

    private void activateCashCard(CashCardDetail cardDetail, OrderDetail orderDetail) throws DataUpdationException {
        if (cardDetail.getCustomerId() == null) {
            cardDetail.setCustomerId(cardDetail.getBuyerId());
            cardDetail.setActivationTime(AppUtils.getCurrentTimestamp());
        }
        cardDetail.setCardStatus(CashCardStatus.ACTIVE.name());
        cashCardDetailDao.save(cardDetail);
        GiftCardActivationRequest giftCardActivationRequest = new GiftCardActivationRequest();
        giftCardActivationRequest.setReason("Activation at the time of purchasing");
        giftCardActivationRequest.setRequestingEmployee(orderDetail.getEmpId());
        giftCardActivationRequest.setUnitId(orderDetail.getUnitId());
        logCashCardEvent(cardDetail, CashCardEventStatus.CARD___ACTIVATION___SUCCESS, giftCardActivationRequest);
    }

    private OrderItemAddon addAddon(com.stpl.tech.kettle.data.kettle.OrderItem order, ProductSource source, ProductClassification type,
                                    IngredientProductDetail detail) {
        OrderItemAddon info = new OrderItemAddon(order, detail.getProduct().getProductId(),
                detail.getProduct().getName(), type == null ? null : type.name(), source.name(),
                detail.getDimension() == null ? null : detail.getDimension().getCode(),
                detail.getUom() == null ? null : detail.getUom().name(), detail.getQuantity(),
                AppConstants.getValue(detail.isDefaultSetting()));
        if(Objects.nonNull(detail.getIngredientProductDetailId()) && detail.getIngredientProductDetailId()!=0) {
            info.setOrderItemAddonId(detail.getIngredientProductDetailId());
        }
        orderItemAddonDao.save(info);
        return info;
    }

    private OrderItemAddon addAddon(com.stpl.tech.kettle.data.kettle.OrderItem order, ProductSource source, ProductClassification type,
                                    IngredientVariantDetail detail) {
        OrderItemAddon info = new OrderItemAddon(order, detail.getId(), detail.getAlias(), type.name(), source.name(),
                null, detail.getUom() == null ? null : detail.getUom().name(), detail.getQuantity(),
                AppConstants.getValue(detail.isDefaultSetting()));
        if(Objects.nonNull(detail.getIngredientVariantDetailId()) && detail.getIngredientVariantDetailId()!=0) {
            info.setOrderItemAddonId(detail.getIngredientVariantDetailId());
        }
        orderItemAddonDao.save(info);
        return info;
    }

    private OrderItemAddon addAddon(com.stpl.tech.kettle.data.kettle.OrderItem order, String name) {
        OrderItemAddon info = new OrderItemAddon(order, -1, name, ProductClassification.FREE_OPTION.name(),
                ProductSource.OPTION.name(), "None", "PC", new BigDecimal(order.getQuantity()), "N");
        if(Objects.nonNull(order.getOrderItemId()) && order.getOrderItemId()!=0){
            try {
                OrderItemAddon orderItemAddon = orderItemAddonDao.findByOrderItemIdAndNameForFreeOption(
                        order.getOrderItemId(), name, ProductClassification.FREE_OPTION.name());
                if (Objects.nonNull(orderItemAddon)) {
                    info.setOrderItemAddonId(orderItemAddon.getOrderItemAddonId());
                }
            }catch (Exception e){
                log.info("Error in setting order item addon id for item id : {} and error is : {}",order.getOrderItemId(),e);
            }
        }
        orderItemAddonDao.save(info);
        return info;
    }

    private List<OrderItemAddon> addAddon(com.stpl.tech.kettle.data.kettle.OrderItem order, OrderItemComposition composition) {
        List<OrderItemAddon> list = new ArrayList<>();

        if (composition != null) {
            if (composition.getVariants() != null && composition.getVariants().size() > 0) {
                for (IngredientVariantDetail detail : composition.getVariants()) {
                    list.add(addAddon(order, ProductSource.SCM, ProductClassification.VARIANT, detail));
                }
            }
            if (composition.getProducts() != null && composition.getProducts().size() > 0) {
                for (IngredientProductDetail detail : composition.getProducts()) {
                    list.add(addAddon(order, ProductSource.SCM, ProductClassification.PRODUCT_VARIANT, detail));
                }
            }
            if (composition.getAddons() != null && composition.getAddons().size() > 0) {
                for (IngredientProductDetail detail : composition.getAddons()) {
                    list.add(addAddon(order, ProductSource.MENU, detail.getProduct().getClassification(), detail));

                }
            }

            if (composition.getOptions() != null && composition.getOptions().size() > 0) {
                for (String detail : composition.getOptions()) {
                    list.add(addAddon(order, detail));
                }
            }

            /*
             * if (composition.getOthers() != null && composition.getOthers().size() > 0) {
             * for (IngredientProductDetail detail : composition.getOthers()) {
             * list.add(addAddon(order, ProductSource.SCM, ProductClassification.OTHERS,
             * detail)); } }
             */
        }
        return list;
    }

    private List<OrderItemAddon> addMandatoryAddon(com.stpl.tech.kettle.data.kettle.OrderItem order) {
        List<OrderItemAddon> list = new ArrayList<>();
        Collection<IngredientProductDetail> mandatoryAddons = recipeCache.getMandatoryAddons(order.getProductId(),
                order.getDimension(), order.getRecipeProfile());
        if (mandatoryAddons != null && mandatoryAddons.size() > 0) {
            for (IngredientProductDetail detail : mandatoryAddons) {
                list.add(addAddon(order, ProductSource.MENU, detail.getProduct().getClassification(), detail));
            }
        }
        return list;
    }

    private void addAddons(com.stpl.tech.kettle.data.kettle.OrderItem order, OrderItemComposition composition) {
        order.getOrderItemAddons().addAll(addAddon(order, composition));
        order.getOrderItemAddons().addAll(addMandatoryAddon(order));
    }

    private com.stpl.tech.kettle.data.kettle.OrderItem addItem(OrderDetail order, OrderItem item, boolean isComboConsituent, com.stpl.tech.kettle.data.kettle.OrderItem parent,
                                                               boolean suggestWalletGiftCard) throws DataUpdationException {
		com.stpl.tech.kettle.data.kettle.OrderItem info = new com.stpl.tech.kettle.data.kettle.OrderItem();
        setData(order, info, item, isComboConsituent, parent);
        orderItemDao.save(info);
        info.setIsHoldOn(item.getIsHoldOn());
        if(AppConstants.YES.equalsIgnoreCase(item.getIsHoldOn())){
            order.getOrderItemIdOnHold().add(info.getOrderItemId());
        }
        Boolean isCodOrder = TransactionUtils.isCODOrder(order.getOrderSource()) && order.getChannelPartnerId() != AppConstants.BAZAAR_PARTNER_ID;
        Boolean isDineAndSnpOrder = AppUtils.isDineInOrAppOrder(order.getChannelPartnerId());
        orderMetadataService.addOrderItemMetaDataDetails(item,info.getOrderItemId(),order.getOrderId(),order.getCustomerId(),order.getBrandId(),isCodOrder,isDineAndSnpOrder);
        if (AppUtils.isGiftCard(item.getCode())) {
            item.setVoucherCode(item.getItemCode());
            // here2
            updateCashCardDetails(order, item, suggestWalletGiftCard);
            voucherManagementService.updateVoucher(item.getVoucherCode(), item.getItemCode(), item.getCardType());
        }
        addAddons(info, item.getComposition());
        if (Objects.nonNull(item.getHasPreference()) && item.getHasPreference()
                && Objects.nonNull(item.getPreferenceDetail())) {
            addPreferenceToOrderItemAddon(info, item);
        }
        return info;
    }
    private com.stpl.tech.kettle.data.kettle.OrderItem updateItem(OrderDetail order, OrderItem item, boolean isComboConsituent, com.stpl.tech.kettle.data.kettle.OrderItem parent,
                                                               boolean suggestWalletGiftCard) throws DataUpdationException {
        com.stpl.tech.kettle.data.kettle.OrderItem info = new com.stpl.tech.kettle.data.kettle.OrderItem();
        setData(order, info, item, isComboConsituent, parent);
        info.setOrderItemId(item.getItemId());
         info = orderItemDao.save(info);
        Boolean isCodOrder = TransactionUtils.isCODOrder(order.getOrderSource()) && order.getChannelPartnerId() != AppConstants.BAZAAR_PARTNER_ID;
        Boolean isDineAndSnpOrder = AppUtils.isDineInOrAppOrder(order.getChannelPartnerId());
        orderMetadataService.addOrderItemMetaDataDetails(item,info.getOrderItemId(),order.getOrderId(),order.getCustomerId(),order.getBrandId(), isCodOrder,isDineAndSnpOrder);
        if (AppUtils.isGiftCard(item.getCode())) {
            item.setVoucherCode(item.getItemCode());
            // here2
            updateCashCardDetails(order, item, suggestWalletGiftCard);
            voucherManagementService.updateVoucher(item.getVoucherCode(), item.getItemCode(), item.getCardType());
        }
        addAddons(info, item.getComposition());
        if (Objects.nonNull(item.getHasPreference()) && item.getHasPreference()
                && Objects.nonNull(item.getPreferenceDetail())) {
            addPreferenceToOrderItemAddon(info, item);
        }
        return info;
    }

    private OrderItemAddon addPreferenceToOrderItemAddon(com.stpl.tech.kettle.data.kettle.OrderItem order, OrderItem item) {
        OrderItemAddon info = new OrderItemAddon(order, item.getPreferenceDetail().getPreferenceId(),
                item.getPreferenceDetail().getPreferenceName(), item.getPreferenceDetail().getPreferenceType(), null,
                order.getDimension(), null, new BigDecimal(order.getQuantity()), "N");
        orderItemAddonDao.save(info);
        return info;
    }

    private void setTaxInfo(TaxationDetailDao taxDetail, TaxDetail tax) {
        taxDetail.setTaxCode(tax.getCode());
        taxDetail.setTaxType(tax.getType());
        taxDetail.setTotalTax(tax.getValue());
        taxDetail.setTaxPercentage(tax.getPercentage());
        taxDetail.setTotalAmount(tax.getTotal());
        taxDetail.setTaxableAmount(tax.getTaxable());
    }

    private List<OrderTaxDetail> setTaxDetail(OrderDetail detail, List<TaxDetail> taxes) {
        if (taxes == null || taxes.isEmpty()) {
            return null;
        }
        List<OrderTaxDetail> orderTaxDetails = new ArrayList<>();
        for (TaxDetail tax : taxes) {
            OrderTaxDetail taxDetail = new OrderTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderDetail(detail);
            orderTaxDetails.add(orderTaxDetailDao.save(taxDetail));
        }
        return orderTaxDetails;
    }

    private List<OrderTaxDetail> updateTaxDetail(OrderDetail detail, List<TaxDetail> taxes) {
        if (taxes == null || taxes.isEmpty()) {
            return null;
        }
        List<OrderTaxDetail> orderTaxDetails = new ArrayList<>();
        for (TaxDetail tax : taxes) {
            OrderTaxDetail taxDetail = new OrderTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderDetail(detail);
            orderTaxDetails.add(orderTaxDetailDao.save(taxDetail));
        }
        return orderTaxDetails;
    }

    private void setTaxDetail(int orderId, int stateId, com.stpl.tech.kettle.data.kettle.OrderItem detail, OrderItem item,
                              Map<String, OrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return;
        }
        for (TaxDetail tax : item.getTaxes()) {
            OrderItemTaxDetail taxDetail = new OrderItemTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderItem(detail);
            itemTaxDetailDao.save(taxDetail);
        }
        setTaxInvoices(orderId, stateId, detail, item, invoices);
    }

    private void updateTaxDetail(int orderId, int stateId, com.stpl.tech.kettle.data.kettle.OrderItem detail, OrderItem item,
                              Map<String, OrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return;
        }
        for (TaxDetail tax : item.getTaxes()) {
            OrderItemTaxDetail taxDetail = new OrderItemTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderItem(detail);
            try {
                OrderItemTaxDetail orderItemTaxDetail = itemTaxDetailDao.findByOrderItemIdAndTaxCode(detail.getOrderItemId(),tax.getCode());
                if(Objects.nonNull(orderItemTaxDetail) && orderItemTaxDetail.getOrderItemTaxDetailId()!=0){
                    taxDetail.setOrderItemTaxDetailId(orderItemTaxDetail.getOrderItemTaxDetailId());
                }
            }catch (Exception e){
                log.info("Error in fetching order item tax detail for order item id : {} and error is :{}",detail.getOrderItemId(),e);
            }
            itemTaxDetailDao.save(taxDetail);
        }
        setTaxInvoices(orderId, stateId, detail, item, invoices);
    }

    private void setTaxInvoices(int orderId, int stateId, com.stpl.tech.kettle.data.kettle.OrderItem detail, OrderItem item,
                                Map<String, OrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()
                || (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().isIsComplimentary()
                && item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO)) {
            return;
        }
        if (!invoices.containsKey(detail.getTaxCode())) {
            OrderItemInvoice invoice = new OrderItemInvoice();
            invoice.setStateId(stateId);
            invoice.setOrderId(orderId);
            invoice.setStateInvoiceId(stateSequenceIdDao.getNextStateInvoiceId(stateId));
            invoice.setTaxableAmount(BigDecimal.ZERO);
            invoice.setTaxAmount(BigDecimal.ZERO);
            invoice.setTaxCategory(detail.getTaxCode());
            invoice.setTotalAmount(BigDecimal.ZERO);
            invoices.put(detail.getTaxCode(), invoice);
            orderItemInvoiceDao.save(invoice);
        }
        OrderItemInvoice data = invoices.get(detail.getTaxCode());
        data.setTotalAmount(data.getTotalAmount().add(item.getTotalAmount()));
        data.setTaxableAmount(data.getTaxableAmount().add(item.getAmount()));
        data.setTaxAmount(data.getTaxAmount().add(item.getTax()));
        for (TaxDetail tax : item.getTaxes()) {
            boolean found = false;
            List<OrderItemInvoiceTaxDetail> taxInvoices = data.getOrderItemInvoiceTaxes();
            for (OrderItemInvoiceTaxDetail taxInvoice : taxInvoices) {
                if (taxInvoice.getTaxCode().equals(tax.getCode()) && taxInvoice.getTaxType().equals(tax.getType())
                        && taxInvoice.getTaxPercentage().equals(tax.getPercentage())) {
                    found = true;
                    taxInvoice.setTotalAmount(taxInvoice.getTotalAmount().add(tax.getTotal()));
                    taxInvoice.setTaxableAmount(taxInvoice.getTaxableAmount().add(tax.getTaxable()));
                    taxInvoice.setTotalTax(taxInvoice.getTotalTax().add(tax.getValue()));
                    break;
                }

            }
            if (!found) {
                OrderItemInvoiceTaxDetail newTaxInvoice = new OrderItemInvoiceTaxDetail();
                newTaxInvoice.setOrderItemInvoice(data);
                newTaxInvoice.setTaxableAmount(tax.getTaxable());
                newTaxInvoice.setTaxCode(tax.getCode());
                newTaxInvoice.setTaxPercentage(tax.getPercentage());
                newTaxInvoice.setTaxType(tax.getType());
                newTaxInvoice.setTotalAmount(tax.getTotal());
                newTaxInvoice.setTotalTax(tax.getValue());
                itemInvoiceTaxDetailDao.save(newTaxInvoice);
                data.getOrderItemInvoiceTaxes().add(newTaxInvoice);
            }
        }
    }

    private List<com.stpl.tech.kettle.data.kettle.OrderItem> addItems(int stateId, OrderDetail order, List<OrderItem> items,
                                                                      List<OrderMetadata> metadataList,Map<String, OrderItemInvoice> invoices) throws DataUpdationException {
		List<com.stpl.tech.kettle.data.kettle.OrderItem> objects = new ArrayList<com.stpl.tech.kettle.data.kettle.OrderItem>();

        if (items != null && items.size() > 0) {
            boolean suggestWalletGiftCard = false;
            for (OrderMetadata metadata : metadataList) {
                if (metadata.getAttributeName().equals(AppConstants.WALLET_TYPE)
                        && metadata.getAttributeValue().equals(AppConstants.SUGGEST_WALLET)) {
                    suggestWalletGiftCard = true;
                    break;
                }
            }
            for (OrderItem item : items) {
                // here4
                com.stpl.tech.kettle.data.kettle.OrderItem parent = addItem(order, item, false, null, suggestWalletGiftCard);
                setTaxDetail(order.getOrderId(), stateId, parent, item, invoices);
                parent.setOrderItemInvoice(invoices.get(item.getCode()));
                objects.add(parent);

                if (item.getComposition() != null && item.getComposition().getMenuProducts() != null
                        && item.getComposition().getMenuProducts().size() > 0) {
                    BigDecimal parentOriginalPrice = new BigDecimal(0);
                    for (OrderItem menuItem : item.getComposition().getMenuProducts()) {
                        // here4
                        com.stpl.tech.kettle.data.kettle.OrderItem child = addItem(order, menuItem, true, parent, suggestWalletGiftCard);
                        setTaxDetail(order.getOrderId(), stateId, child, menuItem, invoices);
                        child.setOrderItemInvoice(invoices.get(menuItem.getCode()));
                        objects.add(child);
                        parentOriginalPrice = AppUtils.add(parentOriginalPrice,
                                menuItem.getOriginalPrice() != null ? menuItem.getOriginalPrice()
                                        : menuItem.getPrice());
                    }
                    setParentOriginalPriceCombo(parent.getOrderItemId(), parentOriginalPrice);
                }
            }
        }
        return objects;
    }

    private List<com.stpl.tech.kettle.data.kettle.OrderItem> updateItems(int stateId, OrderDetail order, List<OrderItem> items,
                                                                      List<OrderMetadata> metadataList,Map<String, OrderItemInvoice> invoices) throws DataUpdationException {
        List<com.stpl.tech.kettle.data.kettle.OrderItem> objects = new ArrayList<com.stpl.tech.kettle.data.kettle.OrderItem>();

        if (items != null && items.size() > 0) {
            boolean suggestWalletGiftCard = false;
            for (OrderMetadata metadata : metadataList) {
                if (metadata.getAttributeName().equals(AppConstants.WALLET_TYPE)
                        && metadata.getAttributeValue().equals(AppConstants.SUGGEST_WALLET)) {
                    suggestWalletGiftCard = true;
                    break;
                }
            }
            for (OrderItem item : items) {
                // here4
                com.stpl.tech.kettle.data.kettle.OrderItem parent = updateItem(order, item, false, null, suggestWalletGiftCard);
                updateTaxDetail(order.getOrderId(), stateId, parent, item, invoices);
                parent.setOrderItemInvoice(invoices.get(item.getCode()));
                orderItemDao.save(parent);
                objects.add(parent);

                if (item.getComposition() != null && item.getComposition().getMenuProducts() != null
                        && item.getComposition().getMenuProducts().size() > 0) {
                    BigDecimal parentOriginalPrice = new BigDecimal(0);
                    for (OrderItem menuItem : item.getComposition().getMenuProducts()) {
                        // here4
                        com.stpl.tech.kettle.data.kettle.OrderItem child = updateItem(order, menuItem, true, parent, suggestWalletGiftCard);
                        updateTaxDetail(order.getOrderId(), stateId, child, menuItem, invoices);
                        child.setOrderItemInvoice(invoices.get(menuItem.getCode()));
                        objects.add(child);
                        parentOriginalPrice = AppUtils.add(parentOriginalPrice,
                                menuItem.getOriginalPrice() != null ? menuItem.getOriginalPrice()
                                        : menuItem.getPrice());
                    }
                    setParentOriginalPriceCombo(parent.getOrderItemId(), parentOriginalPrice);
                }
            }
        }
        return objects;
    }

    private void setParentOriginalPriceCombo(Integer orderItemId, BigDecimal parentOriginalPrice) {
        try {
            Optional<com.stpl.tech.kettle.data.kettle.OrderItem> data = orderItemDao.findById(orderItemId);
            if (data.isPresent()) {
                com.stpl.tech.kettle.data.kettle.OrderItem item = data.get();
                item.setOriginalPrice(parentOriginalPrice);
                orderItemDao.save(item);
            }
        } catch (Exception e) {
            log.error("Error while updating the original price for parent Order Item Id {}", orderItemId);
        }
    }

    private List<OrderSettlement> addSettlements(OrderDetail order, List<Settlement> items)
            throws DataUpdationException {
        List<OrderSettlement> objects = new ArrayList<OrderSettlement>();
        if (items != null && items.size() > 0) {
            for (Settlement item : items) {
                objects.add(addSettlement(order, item));
            }
        }
        return objects;
    }
    private List<OrderSettlement> updateSettlements(OrderDetail order, List<Settlement> items)
            throws DataUpdationException {
        List<OrderSettlement> objects = new ArrayList<OrderSettlement>();
        if (items != null && items.size() > 0) {
            for (Settlement item : items) {
                objects.add(updateSettlement(order, item));
            }
        }
        return objects;
    }

    private OrderSettlement updateSettlement(OrderDetail order, Settlement item) throws DataUpdationException {
        return updateSettlement(order, item, null);
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private OrderSettlement updateSettlement(OrderDetail order, Settlement item, OrderSettlement oldSettlement)
            throws DataUpdationException {
        OrderSettlement orderSettlement = null;

        if (oldSettlement != null) {
            orderSettlement = oldSettlement;
        } else {
            orderSettlement = new OrderSettlement();
        }

        updateData(order, orderSettlement, item);
        orderSettlementDao.save(orderSettlement);
        if (item.getExternalSettlements() != null && item.getExternalSettlements().size() > 0) {
            List<OrderExternalSettlementData> settlements = new ArrayList<>();
            for (ExternalSettlement externalSettlement : item.getExternalSettlements()) {
                OrderExternalSettlementData externalSettlementData = new OrderExternalSettlementData(orderSettlement,
                        externalSettlement.getAmount(), externalSettlement.getExternalTransactionId());
                settlementDataDao.save(externalSettlementData);
                settlements.add(externalSettlementData);
            }
            orderSettlement.getExternalTransactions().addAll(settlements);
        }
        for (OrderPaymentDenomination denomination : item.getDenominations()) {
            orderSettlement.getDenominations().add(addOrderPaymentDenominations(order, denomination, orderSettlement));
        }
        if (item.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
            if (order.getCustomerId() <= 5) {
                throw new DataUpdationException("Card Can Only Be settled for a valid customer");
            } else {
                try {
                    //Don't create this while persisting consolidated order, it is already done in checkout table
                    createCashCardEvents(order,item,orderSettlement.getSettlementId());
                } catch (NumberFormatException e) {
                    throw new DataUpdationException(e.getMessage());
                }
            }
        }
        return orderSettlement;
    }

    private OrderSettlement addSettlement(OrderDetail order, Settlement item) throws DataUpdationException {
        return addSettlement(order, item, null);
    }

    private void setData(OrderDetail order, OrderSettlement info, Settlement item) {
        info.setAmountPaid(item.getAmount());
        info.setPaymentModeId(item.getMode());
        BigDecimal vouchers = item.getExtraVouchers() == null ? BigDecimal.ZERO : item.getExtraVouchers();
        info.setExtraVouchers(vouchers);
        info.setOrderDetail(order);
    }

    private void updateData(OrderDetail order, OrderSettlement info, Settlement item) {
        info.setAmountPaid(item.getAmount());
        info.setPaymentModeId(item.getMode());
        BigDecimal vouchers = item.getExtraVouchers() == null ? BigDecimal.ZERO : item.getExtraVouchers();
        info.setExtraVouchers(vouchers);
        info.setOrderDetail(order);
    }

    private OrderPaymentDenominationDetail addOrderPaymentDenominations(OrderDetail order,
                                                                        OrderPaymentDenomination denomination, OrderSettlement orderSettlement) {
        OrderPaymentDenominationDetail detail = new OrderPaymentDenominationDetail();
        detail.setOrderId(order.getOrderId());
        detail.setDenominationId(denomination.getDenominationDetailId());
        detail.setOrderSettlement(orderSettlement);
        detail.setCount(denomination.getCount());
        detail.setTotalAmount(denomination.getTotalAmount());
        paymentDenominationDetailDao.save(detail);
        return detail;
    }

    private void validateSettlement(OrderSettlement orderSettlement, List<ExternalSettlement> externalSettlements)
            throws CardValidationException {
        BigDecimal settlementAmount = orderSettlement.getAmountPaid();
        BigDecimal cardSettlementsAmount = externalSettlements.stream().map(value -> value.getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!AppUtils.isEqual(settlementAmount, cardSettlementsAmount)) {
            throw new CardValidationException("The sum of card amount does not add up to Total Settlement Amount");
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private OrderSettlement addSettlement(OrderDetail order, Settlement item, OrderSettlement oldSettlement)
            throws DataUpdationException {
        OrderSettlement orderSettlement = null;

        if (oldSettlement != null) {
            orderSettlement = oldSettlement;
        } else {
            orderSettlement = new OrderSettlement();
        }

        setData(order, orderSettlement, item);
        orderSettlementDao.save(orderSettlement);
        if (item.getExternalSettlements() != null && item.getExternalSettlements().size() > 0) {
            List<OrderExternalSettlementData> settlements = new ArrayList<>();
            for (ExternalSettlement externalSettlement : item.getExternalSettlements()) {
                OrderExternalSettlementData externalSettlementData = new OrderExternalSettlementData(orderSettlement,
                        externalSettlement.getAmount(), externalSettlement.getExternalTransactionId());
                settlementDataDao.save(externalSettlementData);
                settlements.add(externalSettlementData);
            }
            orderSettlement.getExternalTransactions().addAll(settlements);
        }
        for (OrderPaymentDenomination denomination : item.getDenominations()) {
            orderSettlement.getDenominations().add(addOrderPaymentDenominations(order, denomination, orderSettlement));
        }
        if (item.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
//            if (item.getExternalSettlements() == null || item.getExternalSettlements().size() == 0) {
//                throw new DataUpdationException("Settlement type gift card should have a transaction id");
//            } else
            if (order.getCustomerId() <= 5) {
                throw new DataUpdationException("Card Can Only Be settled for a valid customer");
            } else {
                try {
                    if(!(Objects.nonNull(order.getTableRequestId()) && order.getTableRequestId() > 0)) {
                        createCashCardEvents(order, item, orderSettlement.getSettlementId());
                    }
                } catch (NumberFormatException e) {
                    throw new DataUpdationException(e.getMessage());
                }
            }
        }
        return orderSettlement;
    }


    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private OrderSettlement addTableSettlement(OrderDetail order, Settlement item, OrderSettlement oldSettlement)
            throws DataUpdationException {
        OrderSettlement orderSettlement = null;

        if (oldSettlement != null) {
            orderSettlement = oldSettlement;
        } else {
            orderSettlement = new OrderSettlement();
        }

        setData(order, orderSettlement, item);
        orderSettlementDao.save(orderSettlement);
        if (item.getExternalSettlements() != null && item.getExternalSettlements().size() > 0) {
            List<OrderExternalSettlementData> settlements = new ArrayList<>();
            for (ExternalSettlement externalSettlement : item.getExternalSettlements()) {
                OrderExternalSettlementData externalSettlementData = new OrderExternalSettlementData(orderSettlement,
                        externalSettlement.getAmount(), externalSettlement.getExternalTransactionId());
                settlementDataDao.save(externalSettlementData);
                settlements.add(externalSettlementData);
            }
            orderSettlement.getExternalTransactions().addAll(settlements);
        }
        for (OrderPaymentDenomination denomination : item.getDenominations()) {
            orderSettlement.getDenominations().add(addOrderPaymentDenominations(order, denomination, orderSettlement));
        }
        if (item.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
//            if (item.getExternalSettlements() == null || item.getExternalSettlements().size() == 0) {
//                throw new DataUpdationException("Settlement type gift card should have a transaction id");
//            } else
            if (order.getCustomerId() <= 5) {
                throw new DataUpdationException("Card Can Only Be settled for a valid customer");
            } else {
                try {
                    if((order.getTableRequestId() != null && order.getTableRequestId() > 0)) {
                        createCashCardEvents(order, item, orderSettlement.getSettlementId());
                    }
                } catch (NumberFormatException e) {
                    throw new DataUpdationException(e.getMessage());
                }
            }
        }
        return orderSettlement;
    }

    private void validateCard(CashCardDetail cashCardDetail, int customerId, BigDecimal amount)
            throws CardValidationException {
        // pending (100) - amount (100) = 0
        if (cashCardDetail.getCashPendingAmount().subtract(amount).compareTo(BigDecimal.ZERO) < 0) {
            throw new CardValidationException("Card does not have sufficient value : Card Value "
                    + cashCardDetail.getCashPendingAmount() + " : Amount Charged :" + amount);
        }
        if (cashCardDetail.getCustomerId() != null && cashCardDetail.getCustomerId() != customerId) {
            throw new CardValidationException("Card is not linked with the customer");
        }
        /*
         * if (cashCardDetail.getEndDate().before(AppUtils.getCurrentBusinessDate() )) {
         * throw new CardValidationException("Card has expired on: " +
         * cashCardDetail.getEndDate()); }
         */
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())) {
            throw new CardValidationException("Card has not been purchased.");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.EXPIRED.name())) {
            throw new CardValidationException("Card has expired");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.BLOCKED.name())) {
            throw new CardValidationException("Card is invalid");
        }

    }

    public void createCardEvent(int cashCardId, int customerId, int orderId, int settlementId, BigDecimal amount)
            throws CardValidationException {
        CashCardDetail detail = null;
        Optional<CashCardDetail> data = cashCardDetailDao.findById(cashCardId);
        if (data.isPresent()) {
            detail = data.get();
        }
        validateCard(detail, customerId, amount);
        if (detail.getCustomerId() == null) {
            detail.setCustomerId(customerId);
            detail.setActivationTime(AppUtils.getCurrentTimestamp());
        }
        if (detail.getCardStatus().equals(CashCardStatus.READY_FOR_ACTIVATION.name())) {
            detail.setCardStatus(CashCardStatus.ACTIVE.name());
        }
        detail.setCashPendingAmount(detail.getCashPendingAmount().subtract(amount));
        CashCardEvent event = new CashCardEvent();
        event.setCashCardId(cashCardId);
        event.setOrderId(orderId);
        event.setOrderSettlementId(settlementId);
        event.setSettlementAmount(amount);
        event.setSettlementStatus(AppConstants.ACTIVE);
        event.setSettlementTime(AppUtils.getCurrentTimestamp());
//	        event.setOpeningBalance(AppUtils.add(amount,detail.getCashPendingAmount()));
//	        event.setClosingBalance(detail.getCashPendingAmount());
        cashCardEventDao.save(event);
    }

    private void createEmployeeMeal(OrderDetail detail, Order order) {
        for (OrderItem item : order.getOrders()) {
            EmployeeMealData data = new EmployeeMealData();
            data.setBusinessDate(AppUtils.getBusinessDate());
            data.setDimension(item.getDimension());
            data.setEmployeeId(order.getEmployeeIdForMeal());
            data.setOrderDetail(detail);
            data.setProductId(item.getProductId());
            data.setProductTypeId(productCache.getProductById(item.getProductId()).getType());
            data.setQuantity(item.getQuantity());
            data.setUnitId(order.getUnitId());
            employeeMealDataDao.save(data);
        }

    }

    private void updatePaidEmployeeMealData(OrderDetail detail, Order order) {
        EmployeeMealAllowanceData a = new EmployeeMealAllowanceData();
        a.setOrderId(detail.getOrderId());
        a.setOrdertime(detail.getBillingServerTime());
        a.setEmployeeId(order.getEmployeeIdForMeal());
        a.setAmount(detail.getSettledAmount());
        mealAllowanceDataDao.save(a);
    }

    private void createOrderDiscountMapping(OrderDetail detail, Order order) {
        for (OrderDiscountData orderDiscountData : order.getOrderDiscountData()) {
            PartnerOrderDiscountMapping partnerOrderDiscountMap = new PartnerOrderDiscountMapping();
            partnerOrderDiscountMap.setBrandId(detail.getBrandId());
            partnerOrderDiscountMap.setDiscountName(orderDiscountData.getDiscountName());
            partnerOrderDiscountMap.setDiscountType(orderDiscountData.getDiscountType());
            partnerOrderDiscountMap.setIsPartnerDiscount(orderDiscountData.getIsPartnerDiscount());
            if (orderDiscountData.getDiscountValue() != null) {
                partnerOrderDiscountMap
                        .setDiscountValue(new BigDecimal(Float.toString(orderDiscountData.getDiscountValue())));
            }
            if (orderDiscountData.getDiscountAmount() != null) {
                partnerOrderDiscountMap
                        .setDiscountAmount(new BigDecimal(Float.toString(orderDiscountData.getDiscountAmount())));
            }
            partnerOrderDiscountMap.setDiscountCategory(orderDiscountData.getDiscountCategory());
            if (orderDiscountData.getDiscountAppliedOn() != null) {
                partnerOrderDiscountMap
                        .setDiscountAppliedOn(new BigDecimal(Float.toString(orderDiscountData.getDiscountAppliedOn())));
            }
            if (orderDiscountData.getDiscountIsTaxed() != null) {
                partnerOrderDiscountMap.setDiscountIsTaxed(orderDiscountData.getDiscountIsTaxed().toString());
            }
            partnerOrderDiscountMap.setOrderDetail(detail);
            partnerOrderDiscountMap.setVoucherCode(orderDiscountData.getVoucherCode());
            partnerOrderDiscountMap.setPartnerName(orderDiscountData.getPartnerName());

            discountMappingDao.save(partnerOrderDiscountMap);
        }

    }

    private OrderDetail addTableOrder(Unit unit, Order order, Date currentTimestamp,UnitTableMappingDetail tableMapping , StringBuilder sb)
            throws DataUpdationException {
        int stateId = unit.getLocation().getState().getId();
        long time = System.currentTimeMillis();
        if (order.getOrderId() != null && order.getOrderId() != 0) {
            throw new DataUpdationException(
                    String.format("Cannot create the order that already exists with ID %d ", order.getOrderId()));

        }
        OrderDetail detail = new OrderDetail();
        detail.setUnitOrderId(sequenceIdDao.getNextUnitOrderId(order.getUnitId(), order.getSource()));
        if (unit.isTokenEnabled()) {
            detail.setTokenNumber(tokenSequenceDao.getNextTokenNumber(order.getUnitId(), unit.getTokenLimit()));
        }
        else if(Objects.nonNull(order.getAutoTokenEnabled()) && order.getAutoTokenEnabled()){
            detail.setTokenNumber(tokenSequenceDao.getNextTokenNumber(order.getUnitId(), properties.getTokenMaxLimit()));
        }
        detail.setRefOrderId(order.getRefOrderId());
        detail.setIsInvoice(AppConstants.getValue(false));
        detail.setOrderType(order.getOrderType());
        detail.setLinkedOrderId(order.getLinkedOrderId());
        detail.setBillGenerationTime(currentTimestamp);
        detail.setBillingServerTime(currentTimestamp);
        detail.setBillStartTime(order.getBillStartTime());
        detail.setBillCreationSeconds(order.getBillCreationSeconds());
        detail.setOrderSource(order.getSource());
        detail.setOrderSourceId(order.getSourceId());
        detail.setChannelPartnerId(order.getChannelPartner());
        detail.setDeliveryPartnerId(order.getDeliveryPartner());
        setTransactionDetail(detail, order.getTransactionDetail(),Objects.nonNull(order.getServiceChargeItem()) ? order.getServiceChargeItem().getTax() : BigDecimal.ZERO);
        detail.setEmpId(order.getEmployeeId());
        boolean isTakeAway = !AppConstants.isDineIn(order.getChannelPartner());
        detail.setHasParcel(AppConstants.getValue(isTakeAway));
        detail.setUnitId(order.getUnitId());
        detail.setTerminalId(order.getTerminalId());
        detail.setTableNumber(order.getTableNumber());
        detail.setOrderStatus(order.getStatus().name());
        detail.setSettlementType(order.getSettlementType().name());
        detail.setPrintCount(1);
        detail.setDeliveryAddress(order.getDeliveryAddress());
        detail.setOrderRemark(order.getOrderRemark());
        detail.setGeneratedOrderId(order.getGenerateOrderId());
        detail.setCustomerId(order.getCustomerId());
        detail.setPointsRedeemed(order.getPointsRedeemed());
        detail.setCampaignId(order.getCampaignId());
        detail.setBrandId(order.getBrandId());
        detail.setPartnerCustomerId(order.getPartnerCustomerId());
        if (order.getBillBookNo() != null) {
            detail.setManualBillBookNo(order.getBillBookNo());
        }
        if (isSubscriptionOrder(order)) {
            Optional<SubscriptionDetail> data = subscriptionDetailDao
                    .findById(order.getSubscriptionDetail().getSubscriptionId());
            if (data.isPresent()) {
                detail.setSubscriptionDetail(data.get());
            }
        }
        detail.setOfferCode(order.getOfferCode());
        // MOHIT - Truncated the customer name to 45 characters
        detail.setCustomerName(order.getCustomerName() != null && order.getCustomerName().trim().length() > 0
                ? order.getCustomerName().substring(0,
                order.getCustomerName().length() > 45 ? 45 : order.getCustomerName().length())
                : null);
        detail.setTableRequestId(order.getTableRequestId());
        detail.setGiftCardOrder(AppConstants.getValue(order.isGiftCardOrder()));
        detail.setOrderAttribute(order.getOrderAttribute());
        detail.setSourceVersion(AppConstants.KETTLE_ORDER_VERSION);
        detail.setOrderStatus(OrderStatus.SETTLED.value());
        detail.setNoOfPax(tableMapping.getNoOfPax());

        orderDetailDao.save(detail);
        sb.append("\n&&&&&&&&&& , STEP 1.1, - , Add order detail ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        Map<String, OrderItemInvoice> invoices = new HashMap<>();
        detail.getOrderItems().addAll(updateItems(stateId, detail, order.getOrders(), order.getMetadataList(),invoices));
        if(Objects.nonNull(order.getTransactionDetail().getServiceCharge()) && BigDecimal.ZERO.compareTo(order.getTransactionDetail().getServiceCharge()) != 0 && Objects.nonNull(order.getServiceChargeItem())){
            List<OrderItem> items = new ArrayList<>();
            items.add(order.getServiceChargeItem());
            detail.getOrderItems().addAll(addItems(stateId, detail, items, order.getMetadataList(),invoices));
        }
        sb.append("\n&&&&&&&&&& , STEP 1.2, - , Add Order Item ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        detail.getOrderSettlements().addAll(updateSettlements(detail, order.getSettlements()));
        sb.append(
                "\n&&&&&&&&&& , STEP 1.3, - , Add Order Settlement ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        generateOrderStatusEvent(false, detail.getOrderId(), OrderStatus.INITIATED, order.getStatus(),
                order.getEmployeeId(), order.getEmployeeId(), "Order Created", sb);
        generateOrderStatusEvent(false, detail.getOrderId(), order.getStatus(), OrderStatus.SETTLED,
                order.getEmployeeId(), order.getEmployeeId(), "Table Settled", sb);
        sb.append("\n&&&&&&&&&& , STEP 1.4, - , Add Order Status Event ----------,"
                + (System.currentTimeMillis() - time));
        if (order.getOrderDiscountData() != null && !order.getOrderDiscountData().isEmpty()) {
            time = System.currentTimeMillis();
            createOrderDiscountMapping(detail, order);
            sb.append("\n&&&&&&&&&& , STEP 1.6, - , Create Partner Order Discount Mapping ----------,"
                    + (System.currentTimeMillis() - time));
        }
        time = System.currentTimeMillis();
        detail.setOrderTaxes(updateTaxDetail(detail, order.getTransactionDetail().getTaxes()));
        sb.append("\n&&&&&&&&&& , STEP 1.7, - , Set Order Taxes ----------," + (System.currentTimeMillis() - time));
        return detail;
    }

    private OrderDetail addOrder(Unit unit, Order order, Date currentTimestamp, StringBuilder sb)
            throws DataUpdationException {
        int stateId = unit.getLocation().getState().getId();
        long time = System.currentTimeMillis();
        if (order.getOrderId() != null && order.getOrderId() != 0) {
            throw new DataUpdationException(
                    String.format("Cannot create the order that already exists with ID %d ", order.getOrderId()));

        }
        OrderDetail detail = new OrderDetail();
        detail.setUnitOrderId(sequenceIdDao.getNextUnitOrderId(order.getUnitId(), order.getSource()));
        if (unit.isTokenEnabled()) {
            detail.setTokenNumber(tokenSequenceDao.getNextTokenNumber(order.getUnitId(), unit.getTokenLimit()));
        }
        else if(Objects.nonNull(order.getAutoTokenEnabled()) && order.getAutoTokenEnabled()){
            detail.setTokenNumber(tokenSequenceDao.getNextTokenNumber(order.getUnitId(), properties.getTokenMaxLimit()));
        }
        detail.setRefOrderId(order.getRefOrderId());
        detail.setIsInvoice(AppConstants.getValue(false));
        detail.setOrderType(order.getOrderType());
        detail.setLinkedOrderId(order.getLinkedOrderId());
        detail.setBillGenerationTime(currentTimestamp);
        detail.setBillingServerTime(currentTimestamp);
        detail.setBillStartTime(order.getBillStartTime());
        detail.setBillCreationSeconds(order.getBillCreationSeconds());
        detail.setOrderSource(order.getSource());
        detail.setOrderSourceId(order.getSourceId());
        detail.setChannelPartnerId(order.getChannelPartner());
        detail.setDeliveryPartnerId(order.getDeliveryPartner());
        setTransactionDetail(detail, order.getTransactionDetail(),Objects.nonNull(order.getServiceChargeItem()) ?order.getServiceChargeItem().getTax() : BigDecimal.ZERO);
        detail.setEmpId(order.getEmployeeId());
        boolean isTakeAway = !AppConstants.CAFE.equals(order.getSource());
        detail.setHasParcel(AppConstants.getValue(isTakeAway));
        detail.setUnitId(order.getUnitId());
        detail.setTerminalId(order.getTerminalId());
        detail.setTableNumber(order.getTableNumber());
        detail.setOrderStatus(order.getStatus().name());
        detail.setSettlementType(order.getSettlementType().name());
        detail.setPrintCount(1);
        detail.setDeliveryAddress(order.getDeliveryAddress());
        detail.setOrderRemark(order.getOrderRemark());
        detail.setGeneratedOrderId(order.getGenerateOrderId());
        detail.setCustomerId(order.getCustomerId());
        detail.setPointsRedeemed(order.getPointsRedeemed());
        detail.setCampaignId(order.getCampaignId());
        detail.setBrandId(order.getBrandId());
        detail.setPartnerCustomerId(order.getPartnerCustomerId());
        detail.setNoOfPax(order.getNoOfPax());
        if (order.getBillBookNo() != null) {
            detail.setManualBillBookNo(order.getBillBookNo());
        }
        if (isSubscriptionOrder(order)) {
            Optional<SubscriptionDetail> data = subscriptionDetailDao
                    .findById(order.getSubscriptionDetail().getSubscriptionId());
            if (data.isPresent()) {
                detail.setSubscriptionDetail(data.get());
            }
        }
        detail.setOfferCode(order.getOfferCode());
        // MOHIT - Truncated the customer name to 45 characters
        detail.setCustomerName(order.getCustomerName() != null && order.getCustomerName().trim().length() > 0
                ? order.getCustomerName().substring(0,
                order.getCustomerName().length() > 45 ? 45 : order.getCustomerName().length())
                : null);
        detail.setTableRequestId(order.getTableRequestId());
        detail.setGiftCardOrder(AppConstants.getValue(order.isGiftCardOrder()));
        detail.setOrderAttribute(order.getOrderAttribute());
        detail.setSourceVersion(AppConstants.KETTLE_ORDER_VERSION);
        orderDetailDao.save(detail);
        log.info("No Of Pax For Order id : {} : {} ",detail.getOrderId() , order.getNoOfPax());
        sb.append("\n&&&&&&&&&& , STEP 1.1, - , Add order detail ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        /*
         * Unit unitData = masterCache.getUnit(order.getUnitId()); String code = null;
         * if (unitData.isFreeInternetAccess() &&
         * order.getSource().equals(UnitCategory.CAFE.name())) { code =
         * tempAccessDao.generateAccessCode(detail.getOrderId()); }
         * detail.setTempCode(code);
         */
        // here5
        Map<String, OrderItemInvoice> invoices = new HashMap<>();
        //Adding service charge item to order items
        if(Objects.nonNull(order.getTransactionDetail().getServiceCharge()) && BigDecimal.ZERO.compareTo(order.getTransactionDetail().getServiceCharge()) != 0 && Objects.nonNull(order.getServiceChargeItem())){
            order.getOrders().add(order.getServiceChargeItem());
        }
        detail.getOrderItems().addAll(addItems(stateId, detail, order.getOrders(), order.getMetadataList(),invoices));
        sb.append("\n&&&&&&&&&& , STEP 1.2, - , Add Order Item ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        detail.getOrderSettlements().addAll(addSettlements(detail, order.getSettlements()));
        sb.append(
                "\n&&&&&&&&&& , STEP 1.3, - , Add Order Settlement ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        generateOrderStatusEvent(false, detail.getOrderId(), OrderStatus.INITIATED, order.getStatus(),
                order.getEmployeeId(), order.getEmployeeId(), "Order Created", sb);
        sb.append("\n&&&&&&&&&& , STEP 1.4, - , Add Order Status Event ----------,"
                + (System.currentTimeMillis() - time));
        if (order.isEmployeeMeal() != null && order.isEmployeeMeal()) {
            time = System.currentTimeMillis();
            createEmployeeMeal(detail, order);
            sb.append("\n&&&&&&&&&& , STEP 1.5, - , Create Employee Meal ----------,"
                    + (System.currentTimeMillis() - time));
        }
        if (TransactionUtils.isPaidEmployeeMeal(order)) {
            time = System.currentTimeMillis();
            updatePaidEmployeeMealData(detail, order);
            sb.append("\n&&&&&&&&&& , STEP 1.5.a, - , Create Paid Employee Meal ----------,"
                    + (System.currentTimeMillis() - time));
        }
        if (order.getOrderDiscountData() != null && !order.getOrderDiscountData().isEmpty()) {
            time = System.currentTimeMillis();
            createOrderDiscountMapping(detail, order);
            sb.append("\n&&&&&&&&&& , STEP 1.6, - , Create Partner Order Discount Mapping ----------,"
                    + (System.currentTimeMillis() - time));
        }
        time = System.currentTimeMillis();
        detail.setOrderTaxes(setTaxDetail(detail, order.getTransactionDetail().getTaxes()));
        sb.append("\n&&&&&&&&&& , STEP 1.7, - , Set Order Taxes ----------," + (System.currentTimeMillis() - time));
        return detail;
    }

    public OrderStatusEvent generateOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                                     OrderStatus toStatus, int approvedBy, int generatedBy, String reason, StringBuilder sb) {
        return generateOrderStatusEvent(checkExistingState, orderId, fromStatus, toStatus, approvedBy, generatedBy,
                null, reason, sb);
    }

    private boolean validateOrderStateTransition(OrderStatus fromStatus, OrderStatus toStatus) {
        TransitionData data = new TransitionData();
        data.setFromStateCode(fromStatus.name());
        data.setToStateCode(toStatus.name());
        StateTransitionCache.getInstance().setTransitionState(StateTransitionObject.ORDER, data);
        return TransitionStatus.SUCCESS.equals(data.getStatus());
    }

    public OrderStatusEvent generateOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                                     OrderStatus toStatus, int approvedBy, int generatedBy, String errorStatckTrace, String reason,
                                                     StringBuilder sb) {
        long time = System.currentTimeMillis();
        boolean state = validateOrderStateTransition(fromStatus, toStatus);
        sb.append("\n######### , STEP 1a, - , Validate Order Status Transition ----------,"
                + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        state = state & !statusEventDao.existsOrderStatusEvent(checkExistingState, orderId, fromStatus, toStatus);
        sb.append("\n######### , STEP 1b, - , Check Exists Order Status ----------,"
                + (System.currentTimeMillis() - time));
        Date currentTime = AppUtils.getCurrentTimestamp();
        time = System.currentTimeMillis();
        Date startTime = checkExistingState
                ? orderStatusEventDao.getLastOrderStatusEventTime(orderId, fromStatus, currentTime)
                : currentTime;
        sb.append("\n######### , STEP 1c, - , Get Last Order Status Event ----------,"
                + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        OrderStatusEvent event = new OrderStatusEvent(orderId, fromStatus.name(), toStatus.name(), reason, generatedBy,
                approvedBy, startTime, currentTime,
                state ? TransitionStatus.SUCCESS.name() : TransitionStatus.FAILURE.name(), errorStatckTrace);
        orderStatusEventDao.save(event);
        sb.append(
                "\n######### , STEP 1d, - , Save Order Status Event ----------," + (System.currentTimeMillis() - time));
        return event;
    }

    @Override
    public Set<Integer> getProductIds(Order order) {
        Set<Integer> productIds = new HashSet<>();
        for (OrderItem item : order.getOrders()) {
            if(AppConstants.SKIP_LOYALTY_PRODUCTS.contains(item.getProductId())){
                order.setSkipLoyaltyProducts(true);
            }
            if (!AppUtils.isGiftCard(item.getCode())
                    && productCache.getSubscriptionProductDetailsById(item.getProductId()).isEmpty()) {
                productIds.add(item.getProductId());
            }
        }
        return productIds;
    }

    private CreateOrderResult createOrder(Order order, StringBuilder sb) throws DataUpdationException {
        long time = System.currentTimeMillis();
        Date currentTimestamp = AppUtils.getCurrentTimestamp();
        OrderDetail orderDetail;
        time = System.currentTimeMillis();
        SubscriptionProduct subscriptionProduct = subscriptionPlanService.getSubscriptionProduct(order);
        boolean createSubscription = false;
        Customer customer = null;
        if (subscriptionProduct != null) {
            try {
                customer = customerService.getCustomer(order.getCustomerId());
                if (Objects.isNull(customer)) {
                    throw new DataNotFoundException();
                }else{
                    if(StringUtils.isBlank(customer.getFirstName())){
                        customer.setFirstName(order.getCustomerName());
                        customerService.updateCustomerName(customer);
                    }
                }
            } catch (DataNotFoundException e) {
                unitSessionCache.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
                throw new DataUpdationException("Unable to find customer with id " + order.getCustomerId());
            }
            List<String> errors = subscriptionPlanService.validateSubscriptionRequest(order, customer, null);
            if (!CollectionUtils.isEmpty(errors)) {
                String error = String.format("Errors while punching the subscription order:\n %s",
                        StringUtils.join(errors, ",\n"));
                log.error(error);
                throw new DataUpdationException(error);
            } else {
                log.info("Got the request to purchase subscription for order id {} and customer id {}",
                        order.getGenerateOrderId(), order.getCustomerId());
                createSubscription = true;
            }
        }

        sb.append("\n########## , STEP A, - , Check for subscription purchase request ----------,"
                + (System.currentTimeMillis() - time));
        /*
         * Get Brand MetaData to set and validate sms,email,nsp settings
         */
        if (TransactionUtils.isCODOrder(order.getSource())
                && order.getChannelPartner() != AppConstants.BAZAAR_PARTNER_ID) {
            if (order.getTransactionDetail().getTotalAmount()
                    .compareTo(order.getTransactionDetail().getTaxableAmount()) != 0) {
                order.setOfferCode(unitCacheService.getChannelPartnerById(order.getChannelPartner()).getCode());
            }
        }
        String orderFeedbackType = properties.getOrderFeedbackType();
        Brand brand = brandMetaDataCache.getBrandMetaData().get(order.getBrandId());
        Unit unit = null;
        try {
            unit = unitCacheService.getUnitById(order.getUnitId());
            time = System.currentTimeMillis();
            orderDetail = addOrder(unit, order, currentTimestamp, sb);
            sb.append("\n########## , STEP A, - , Add Order ----------," + (System.currentTimeMillis() - time));
        } catch (Exception e) {
            unitSessionCache.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
            throw e;
        }
        time = System.currentTimeMillis();
        enquiryItemService.addOrderEnquiryItems(order, orderDetail.getOrderId());
        sb.append("\n########## , STEP B, - , Add Enquiry Items ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        orderMetadataService.addMetadataDetails(order, orderDetail.getOrderId());
        sb.append("\n########## , STEP C, - , Add Metadata Details ----------," + (System.currentTimeMillis() - time));
        CreateOrderResult result = new CreateOrderResult();
        result.setOrderId(orderDetail.getOrderId());
        result.setFeedbackId(-1);
        result.setGenerateQRCode(false);
        result.setGenerateInAppFeedback(false);
        for(Integer in : orderDetail.getOrderItemIdOnHold()){
            result.getOrderItemIdsToBeOnHold().add(in);
        }
        try {
            if (order.getCustomerId() > 5 && !TransactionUtils.isSpecialOrder(order) && !isSubscriptionOrder(order)) {
                Set<Integer> productIds = getProductIds(order);
                if (Objects.isNull(customer)) {
                    time = System.currentTimeMillis();
                    customer = customerService.getCustomer(order.getCustomerId());
                    if (Objects.isNull(customer)) {
                        throw new DataNotFoundException();
                    } else{
                    if(StringUtils.isBlank(customer.getFirstName())){
                        customer.setFirstName(order.getCustomerName());
                        customerService.updateCustomerName(customer);
                    }
                }
                    sb.append("\n########## , STEP D, - , Customer Lookup ----------,"
                            + (System.currentTimeMillis() - time));
                }

                if (order.getBrandId() == 1 && !customer.isChaayosCustomer()) {
                    // set is chaayos customer flag to true
                    customerDao.updateIsChaayosCustomer(customer.getId(), AppUtils.YES);
                }
                // check if loyalty is allowed for brand
                // ---------------------------------------------------------------------------------------------------------------------------
                time = System.currentTimeMillis();
                if (brand.getAwardLoyalty() && Objects.nonNull(order.getTransactionDetail()) && !order.isSkipLoyaltyProducts()
                && !TransactionUtils.isTableOrder(order)) {
                    if(order.isBypassLoyateaAward() && !productIds.isEmpty() &&
                            (Objects.nonNull(orderDetail.getOfferCode()) || Objects.nonNull(orderDetail.getDiscountReason()))){
                        order.setOrderId(orderDetail.getOrderId());
                        loyaltyService.handleFailedLoyaltyAwardOrder(order, LoyaltyFailedReason.COUPON_REDEEMED.name(),!productIds.isEmpty());
                    }
                    else if(Objects.nonNull(order.getCashBackReceived()) && order.getCashBackReceived() && properties.getLoyaltyFlagForCashBack()){
                        order.setOrderId(orderDetail.getOrderId());
                        loyaltyService.handleFailedLoyaltyAwardOrder(order, LoyaltyFailedReason.CASHBACK_AWARDED.name(),!productIds.isEmpty());
                    }
                    else {
                        loyaltyService.awardloyalty(productIds, order, customer, orderDetail, order.isForceAwardLoyalty());
                        sb.append("\n########## , STEP E.1, - , Customer Update Loyalty Award----------,"
                                + (System.currentTimeMillis() - time));
                    }
                } else {
                    sb.append("\n########## , STEP E.1, - ,Skipping Customer Update Loyalty Award---------- ,"
                            + brand.getBrandName() + " " + (System.currentTimeMillis() - time));
                }
                if(order.isSkipLoyaltyProducts()){
                    offerManagementService.removeSecondFreeChai(order.getCustomerId(), SignupOfferStatus.EXPIRED.name());
                }
                if (brand.getAwardLoyalty() && !TransactionUtils.isTableOrder(order)) {
                    if (order.getPointsRedeemed() != 0) {
                        time = System.currentTimeMillis();
                        boolean haveValidLoyaltyPoints;
                        if(Objects.nonNull(order.getOfferAccountType()) && AppConstants.LOYALTY_OFFER.equals(order.getOfferAccountType()) && Objects.nonNull(order.getPointsRedeemed())
                           && order.getPointsRedeemed() < 0){
                            haveValidLoyaltyPoints = loyaltyService.updateScore(customer.getId(),
                                    LoyaltyEventType.LOYALTY_OFFER,order.getPointsRedeemed(),
                                    orderDetail.getOrderId(),false,productIds.size()>0);
                        }else {
                            haveValidLoyaltyPoints = loyaltyService.updateScore(customer.getId(),
                                    LoyaltyEventType.REGULAR_REDEMPTION, order.getPointsRedeemed(),
                                    orderDetail.getOrderId(), false, productIds.size() > 0);
                        }
                        if (!haveValidLoyaltyPoints) {
                            log.info("########## Customer Don't have enough Loyalty Points for this order");
                            throw new DataUpdationException("Customer Don't have enough Loyalty Points for this order");
                        }
                        sb.append("\n########## , STEP E.2, - , Customer Update Loyalty Redemption----------,"
                                + (System.currentTimeMillis() - time));

                    } else {
                        sb.append("\n########## , STEP E.2, - ,Skipping Customer Update Loyalty Redemption---------- ,"
                                + brand.getBrandName());
                    }

                    /*
                     * if (props.getAutomatedFeedbackSMSForAll() || order.isNewCustomer()) { time =
                     * System.currentTimeMillis();
                     *
                     * if (productIds.size() > 0) {
                     * feedbackManagementDao.generateFeedbackData(orderDetail.getOrderId(),
                     * orderDetail.getUnitId(), orderDetail.getOrderSource(), productIds, customer,
                     * currentTimestamp, FeedbackEventType.REGULAR, FeedbackSource.SMS); }
                     * System.out.
                     * println("\n########## , STEP 6.1, - , Customer Feedback Creation----------,"
                     * + (System.currentTimeMillis() - time)); }
                     */
                    // check whether nps should be send or not
                    log.info("Should NPS be sent for + " + brand.getBrandName() + " ? " + brand.getSendNPS());
                    if (brand.getSendNPS()) {
                        feedbackManagementService.createNPSFeedBack(productIds, customer, orderDetail, currentTimestamp,
                                orderFeedbackType, result, order, sb);
                    } else {
                        log.error("NPS Skipped for brand " + brand.getBrandName());
                    }

                    notificationService.triggerEmail(brand, orderDetail, order, customer, currentTimestamp, sb);

                } else {
                    //
                    time = System.currentTimeMillis();
                    if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(orderDetail.getCustomerId())
                            && !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
                        notificationService.generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(),
                                1, properties.getUndeliveredEmail(), true, true, currentTimestamp, null);
                    }
                    sb.append("\n########## , STEP H, - , Customer Email Event Creation 1----------,"
                            + (System.currentTimeMillis() - time));
                    int reasonId = -1;
                    for (OrderItem item : order.getOrders()) {
                        if (item.getComplimentaryDetail().isIsComplimentary()
                                && item.getComplimentaryDetail().getReasonCode() != null
                                && item.getComplimentaryDetail()
                                .getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
                            reasonId = item.getComplimentaryDetail().getReasonCode();
                            break;
                        }
                    }
                    if (reasonId != -1 && reasonId == 2107) {
                        notificationService.sendCharityOrderNotification(orderDetail, currentTimestamp);
                    }
                }
            }
        } catch (DataNotFoundException e) {
            if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
                    && !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
                notificationService.generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1,
                        null, true, currentTimestamp);
            }
        }
        Set<Integer> paymentIdSet = null;
        paymentGatewayService.processOrderPayment(order, paymentIdSet, orderDetail, sb);

        if (order.getOptionResultEventId() != null) {
            time = System.currentTimeMillis();
            rulesEventService.attach(order.getOptionResultEventId(), orderDetail.getOrderId());
            sb.append("\n########## , STEP J, - , Time Taken To Link The Recommendation----------,"
                    + (System.currentTimeMillis() - time));
        }

        if (TransactionUtils.isTableOrder(order)) {
            if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), order.getOrders().get(0).getCardType())) {
                // do not attach table for gyftr gift card orders
                orderDetail.setTableNumber(null);
                orderDetail.setTableRequestId(null);
            }
            else {
                tableDataService.addOrderTableMapping(orderDetail, order.getTableRequestId(), customer , order.getLoyalteaToBeRedeemed());
                tableDataService.initiateOrderItemStatus(orderDetail,order.getTableRequestId());
            }
        }else if(AppConstants.isDineIn(order.getChannelPartner())){
            tableDataService.initiateOrderItemStatus(orderDetail,order.getTableRequestId());
        }

        cashBackService.processCashRedemption(order, orderDetail, sb);

        if (Objects.nonNull(customer) && !customer.isReferrerAwarded() && Objects.nonNull(customer.getReferrerId())) {
            // activate referral on transaction
            time = System.currentTimeMillis();
            cashPacketService.activateReferralCashPacket(order.getCustomerId(), customer.getReferrerId(),
                    orderDetail.getOrderId());
            sb.append("\n########## , STEP K, - , Time Taken To activate cash referral----------,"
                    + (System.currentTimeMillis() - time));
        }

        List<String> newCards = new ArrayList<>();
        for (OrderItem item : order.getOrders()) {
            if (item.getCardType() != null && (CashCardType.ECARD.name().equalsIgnoreCase(item.getCardType()))
                    || voucherManagementService.isGyftrCard(item.getCardType())) {
                newCards.add(item.getItemCode());
            }
        }
        if (createSubscription) {
            time = System.currentTimeMillis();
            SubscriptionPlan plan = subscriptionPlanService.createSubscription(orderDetail, subscriptionProduct,
                    customer);
            sb.append("\n########## , STEP L, - , Create subscription purchase request ----------,"
                    + (System.currentTimeMillis() - time));
            result.setSubscriptionPlan(plan);
        }
        result.setGiftCard(newCards);
        return result;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public OrderInfo createSubscrptionOrder(Order order, WalletOrder wallet, OrderDomain orderDetail,
			String randomString, OrderUnitMapping orderUnitMapping) throws DataNotFoundException, DataUpdationException,
			CardValidationException, TemplateRenderingException, JMSException, OfferValidationException {
		long time = System.currentTimeMillis();
		OrderInfo walletOrderInfo = null;
		if (validateWalletOrder(wallet, order)) {
			Order walletOrder = extractWalletOrder(order, WalletOrderType.MICRO, wallet);
			walletOrderInfo = processPlainOrder(walletOrder, orderDetail.isIncludeReceipts(),
					orderDetail.isAddMetadata(), orderDetail.getOrderNotification(), randomString, orderUnitMapping);
		}
		Order subscriptionOrder = extractSelectOrder(order);
		if (Objects.nonNull(walletOrderInfo)) {
			subscriptionOrder.setRefOrderId(walletOrderInfo.getOrder().getOrderId());
		}
		OrderInfo subscriptionOrderInfo = processPlainOrder(subscriptionOrder, orderDetail.isIncludeReceipts(),
				orderDetail.isAddMetadata(), orderDetail.getOrderNotification(), randomString, orderUnitMapping);
		if (Objects.nonNull(walletOrderInfo)) {
			subscriptionOrderInfo.getRefrenceOrderIds().add(walletOrderInfo.getOrder().getOrderId());
		}
		order.setRefOrderId(subscriptionOrderInfo.getOrder().getOrderId());
		setForceLoyalty(order, walletOrderInfo, subscriptionOrder);
		OrderInfo orderInfo = null;
		if (!CollectionUtils.isEmpty(order.getOrders())) {
			orderInfo = processPlainOrder(order, orderDetail.isIncludeReceipts(), orderDetail.isAddMetadata(),
					orderDetail.getOrderNotification(), randomString, orderUnitMapping);
			orderInfo.getRefrenceOrderIds().addAll(subscriptionOrderInfo.getRefrenceOrderIds());
			orderInfo.getRefrenceOrderIds().add(subscriptionOrderInfo.getOrder().getOrderId());
			if (orderDetail.isIncludeReceipts()) {
				if (Objects.nonNull(subscriptionOrderInfo)) {
					orderInfo = addSubscriptionForReciept(orderInfo, subscriptionOrderInfo);
				}
			}
		}
		if (Objects.isNull(orderInfo)) {
			orderInfo = subscriptionOrderInfo;
		}
//		else if (Objects.nonNull(subscriptionOrderInfo.getSubscriptionPlan())) {
//			orderInfo.setSubscriptionPlan(subscriptionOrderInfo.getSubscriptionPlan());
//		}
		if (orderDetail.isIncludeReceipts()) {
			if (Objects.nonNull(walletOrderInfo)) {
				addwalletForReciept(orderInfo, walletOrderInfo, WalletOrderType.MICRO);
			}else{
                if((Objects.nonNull(orderInfo.getCashCardPendingAmt()) && orderInfo.getCashCardPendingAmt().compareTo(BigDecimal.ZERO)==0) || Objects.isNull(orderInfo.getCashCardPendingAmt())){
                    orderInfo.setCashCardPendingAmt(orderDetail.getOrder().getCurrentWalletAmount());
                }
            }
			OrderInfo receiptInfo = generateReciept(orderInfo);
			orderInfo.setReceipts(receiptInfo.getReceipts());
			orderInfo.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
			orderInfo.setAndroidReceipts(receiptInfo.getAndroidReceipts());
		}
		log.info("Full Order Processing Time for {} :: {} millisecond ", orderInfo.getOrder().getGenerateOrderId(),
				System.currentTimeMillis() - time);

		if (MapUtils.isEmpty(orderInfo.getOrderNotificationMap())) {
			orderInfo.setOrderNotificationMap(new HashMap<>());
		}
		if (Objects.nonNull(orderInfo)) {
			orderInfo.getOrderNotificationMap().put(orderInfo.getOrder().getOrderId(),
					orderInfo.getOrderNotification());
		}
		if (Objects.nonNull(subscriptionOrderInfo)) {
			orderInfo.getOrderNotificationMap().put(subscriptionOrderInfo.getOrder().getOrderId(),
					subscriptionOrderInfo.getOrderNotification());
		}
		if (Objects.nonNull(walletOrderInfo)) {
			orderInfo.getOrderNotificationMap().put(walletOrderInfo.getOrder().getOrderId(),
					walletOrderInfo.getOrderNotification());
		}
		return orderInfo;
	}

    private void setForceLoyalty(Order order, OrderInfo walletOrderInfo, Order subscriptionOrder) {
        BigDecimal loyaltyAwardAmountThreshold = null;
        try {
            loyaltyAwardAmountThreshold = BigDecimal.valueOf(Integer.parseInt(
                    unitCacheService.getCacheReferenceMetadata(CacheReferenceType.LOYALTY_AWARD_THRESHOLD_AMOUNT)));
        } catch (Exception e) {
            loyaltyAwardAmountThreshold = BigDecimal.valueOf(properties.defaultLoyaltyThreshold());
        }

        boolean forceLoyalty = (Objects.nonNull(order.getTransactionDetail().getPaidAmount())
                && order.getTransactionDetail().getPaidAmount().compareTo(loyaltyAwardAmountThreshold) < 0)
                && ((Objects.nonNull(walletOrderInfo) && Objects.nonNull(walletOrderInfo.getOrder())
                && Objects.nonNull(walletOrderInfo.getOrder().getTransactionDetail().getPaidAmount())
                && walletOrderInfo.getOrder().getTransactionDetail().getPaidAmount()
                .compareTo(loyaltyAwardAmountThreshold) > 0)
                || (Objects.nonNull(subscriptionOrder)
                && Objects.nonNull(subscriptionOrder.getTransactionDetail().getPaidAmount())
                && subscriptionOrder.getTransactionDetail().getPaidAmount()
                .compareTo(loyaltyAwardAmountThreshold) > 0));
        order.setForceAwardLoyalty(forceLoyalty);
    }

    @Override
    public Order extractSelectOrder(Order order) {
        return orderConverter.extractSelectOrder(order);
    }

    @Override
    public boolean validateWalletOrder(WalletOrder wallet, Order order) throws OfferValidationException {
        if (Objects.nonNull(wallet)) {
            if(AppConstants.ORDER_TYPE_COMPLIMENTARY_GIFTCARD.equals(order.getOrderType()) && (Objects.nonNull(wallet.getRechargeAmount()))){
                if (Objects.isNull(order.getCustomerId())) {
                    throw new OfferValidationException("Suggested wallet Offer order for rejected customer");
                }
                return true;
            }

            if ((Objects.nonNull(wallet.getRechargeAmount())
                    && wallet.getRechargeAmount().compareTo(BigDecimal.ZERO) == 1)
                    || (Objects.nonNull(wallet.getSuggestedAmount())
                    && wallet.getSuggestedAmount().compareTo(BigDecimal.ZERO) == 1)) {
//				try {
//					if (Objects.nonNull(order.getCustomerId()) && Objects.nonNull(order.getBrandId())
//							&& Objects.nonNull(wallet.getCustomerPayableAmount())) {
//						WalletSuggestionCustomerInfo customerData = new WalletSuggestionCustomerInfo(
//								order.getCustomerId().toString(), order.getBrandId().toString(),
//								wallet.getCustomerPayableAmount().toBigInteger().toString());
//						Map<Integer, DenominationOfferPercentage> denominationDetail = customerOfferManagementService
//								.getDenominationOffer().getDenominationOfferMap();
//						WalletRecommendationDetail details = customerOfferManagementService
//								.getSuggestWalletOfferExtraAmount(customerData);
//						if (!details.getExtraAmount().equals(wallet.getOfferAmount().toBigInteger().intValue())
//								|| !denominationDetail.get(wallet.getRechargeAmount().intValue()).getExtraValue()
//										.equals(wallet.getExtraAmount().intValue())) {
//							throw new OfferValidationException("Wallet extra ammount validation failed",
//									WebErrorCode.WALLET_RECOMMENDATION_FAILED);
//						}
//					}
//				} catch (Exception e) {
//					throw new OfferValidationException("Suggested wallet Offer validation failed", e);
//				}
                if (Objects.isNull(order.getCustomerId())) {
                    throw new OfferValidationException("Suggested wallet Offer order for rejected customer");
                }
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private void allotCashBack(Order order, CreateOrderResult result, OrderInfo info,
                               OrderNotification orderNotification) {
        log.info("Allocating Cash Back to customer for order {} and customer id");
        if (order.getCashBackReceived()) {
            try {
                log.info(
                        "Added Cash Back to customer for order {} and customer id {} and amount {} and startDate {} and Enddate {}",
                        order.getOrderId(), order.getCustomerId(), order.getCashBackAwarded(),
                        order.getCashBackStartDate(), order.getCashBackEndDate());
                if(Objects.nonNull(order.getCustomerId()) && AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())){
                    log.info("Will not add cashback as customer is in excluded list :: customer id = {}",order.getCustomerId());
                    return ;
                }
                Date startDate = Objects.nonNull(order.getCashBackStartDate()) ? order.getCashBackStartDate() : properties.getCashBackStartDate();
                Date endDate = Objects.nonNull(order.getCashBackEndDate()) ? order.getCashBackEndDate() : properties.getCashBackEndDate();
                boolean awarded = cashBackService.allotCashBack(order.getCashBackAwarded(), order.getCustomerId(),
                        result.getOrderId(), startDate, endDate, order.getCashBackLagDays());
                if (awarded) {
                    order.setCashBackAwarded(order.getCashBackAwarded());
                    order.setCashBackReceived(true);
                    order.setCashBackStartDate(startDate);
                    order.setCashBackEndDate(endDate);
                    // sendCustomerNotification
                    try {

                        String startDateString = AppUtils.dateInddthMMMFormat(startDate);
                        String endDateString = AppUtils.dateInddthMMMFormat(endDate);
                        sendCashBackNotification(info, order.getCashBackAwarded(), startDateString, endDateString,orderNotification);

                    } catch (Exception e) {
                        log.error("Error in sending cash back notification");
                    }
                } else {
                    order.setCashBackAwarded(null);
                    order.setCashBackReceived(false);
                    order.setCashBackStartDate(null);
                    order.setCashBackEndDate(null);
                }
                info.getOrder().setCashBackReceived(order.getCashBackReceived());
                info.getOrder().setCashBackAwarded(order.getCashBackAwarded());
                info.getOrder().setCashBackEndDate(order.getCashBackEndDate());
                info.getOrder().setCashBackStartDate(order.getCashBackStartDate());
            } catch (Exception e) {
                log.error("Error in assigning cash back ", e);
            }
        }
    }

    private void getOrderMetadataNotificationPayload(OrderInfo info, OrderNotification orderNotification) {
        if (Objects.nonNull(info) && Objects.nonNull(orderNotification)) {
            try {
                orderNotification.setCustomerName(info.getCustomer().getFirstName());
                orderNotification.setCafeName(info.getUnit().getName());
                orderNotification.setCustomerContactNumber(info.getCustomer().getContactNumber());
                orderNotification.setGenerateOrderId(info.getOrder().getGenerateOrderId());
                boolean isCustomerAvilable = info.getCustomer().isSmsSubscriber() && !info.getCustomer().isBlacklisted()
                        && !info.getCustomer().getIsDND();
                if (Objects.nonNull(info.getCustomer().getOptWhatsapp())
                        && AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())) {
                    orderNotification.setWhatsAppOptIn(true);
                }
                if ((Objects.isNull(info.getCustomer().getOptWhatsapp())
                        || (Objects.nonNull(info.getCustomer().getOptWhatsapp())
                        && AppConstants.NO.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())))
                        && isCustomerAvilable) {
                    orderNotification.setSmsSubscriber(true);
                }
            } catch (Exception e) {
                log.info("Exception ocurred during getOrderMetadataNotificationPayload for order {} of customer {} ",
                        info.getOrder().getOrderId(), info.getOrder().getCustomerId());
            }
        }
    }

    private void addwalletForReciept(OrderInfo order, OrderInfo walletOrder, WalletOrderType type) {
        if (type.equals(WalletOrderType.MICRO)) {
            order.setCashCardDeductedAmt(order.getOrder().getTransactionDetail().getPaidAmount());
            order.setCashCardExtraAmt(Objects.nonNull(walletOrder.getOrder().getCashCardExtraAmt())?walletOrder.getOrder().getCashCardExtraAmt():BigDecimal.ZERO);
            order.setCashCardPurchaseAmt(walletOrder.getOrder().getTransactionDetail().getPaidAmount());
            order.setCashCardPrevAmt(order.getCashCardPrevAmt().subtract(order.getCashCardPurchaseAmt().add(order.getCashCardExtraAmt())));
            order.getOrder().setSettlements(walletOrder.getOrder().getSettlements());
        } else if (type.equals(WalletOrderType.DIRECT)) {
            List<CashCardDetail> cashCards = cardService.getActiveCashCards(order.getOrder().getCustomerId());
            BigDecimal pendingAmt = BigDecimal.ZERO;
            for (CashCardDetail cardDetail : cashCards) {
                pendingAmt = pendingAmt.add(cardDetail.getCashPendingAmount());
            }
            order.setCashCardPendingAmt(pendingAmt);
            order.setCashCardPrevAmt(pendingAmt.subtract(cashCards.get(cashCards.size()-1).getCashPendingAmount()));
        }
    }

    private OrderInfo addSubscriptionForReciept(OrderInfo order, OrderInfo subscriptionOrder) {
        OrderInfo data = SerializationUtils.clone(order);
        data.getOrder().getOrders().addAll(subscriptionOrder.getOrder().getOrders());
        TransactionDetail orderTransactions = data.getOrder().getTransactionDetail();
        TransactionDetail subscriptionTransactions = subscriptionOrder.getOrder().getTransactionDetail();
        orderTransactions
                .setTotalAmount(subscriptionTransactions.getTotalAmount().add(orderTransactions.getTotalAmount()));
        orderTransactions.setTaxableAmount(
                subscriptionTransactions.getTaxableAmount().add(orderTransactions.getTaxableAmount()));
        orderTransactions
                .setPaidAmount(subscriptionTransactions.getPaidAmount().add(orderTransactions.getPaidAmount()));
        orderTransactions.setSavings(subscriptionTransactions.getSavings().add(orderTransactions.getSavings()));
        orderTransactions.setTax(subscriptionTransactions.getTax().add(subscriptionTransactions.getTax()));
        Set<TaxDetail> added = new HashSet<>();
        for (TaxDetail taxDetail : orderTransactions.getTaxes()) {
            for (TaxDetail subscriptionTaxDetail : subscriptionTransactions.getTaxes()) {
                if (subscriptionTaxDetail.getCode().equalsIgnoreCase(taxDetail.getCode())
                        && subscriptionTaxDetail.getType().equals(taxDetail.getType())
                        && subscriptionTaxDetail.getPercentage().compareTo(taxDetail.getPercentage()) == 0) {
                    taxDetail.setTaxable(subscriptionTaxDetail.getTaxable().add(taxDetail.getTaxable()));
                    taxDetail.setTotal(subscriptionTaxDetail.getTotal().add(taxDetail.getTotal()));
                    taxDetail.setValue(subscriptionTaxDetail.getValue().add(taxDetail.getValue()));
                    added.add(subscriptionTaxDetail);
                }
            }
        }
        subscriptionTransactions.getTaxes().removeAll(added);
        orderTransactions.getTaxes().addAll(subscriptionTransactions.getTaxes());

        for (Settlement subscriptionSettlement : subscriptionOrder.getOrder().getSettlements()) {
            boolean tobeAdded = true;
            for (Settlement settlement : data.getOrder().getSettlements()) {
                if (settlement.getMode() == subscriptionSettlement.getMode()) {
                    settlement.setAmount(subscriptionSettlement.getAmount().add(settlement.getAmount()));
                    tobeAdded = false;
                }
            }
            if (tobeAdded) {
                data.getOrder().getSettlements().add(subscriptionSettlement);
            }
        }

        return data;
    }

    private void handleNullAddonsAndVariants(IngredientProductDetail detail, RecipeDetail recipeDetail) {
        if(Objects.nonNull(recipeDetail)) {
            if (Objects.nonNull(recipeDetail.getAddons()) && !CollectionUtils.isEmpty(recipeDetail.getAddons())) {
                for (IngredientProductDetail addon : recipeDetail.getAddons()) {
                    if (addon.getProductId() == detail.getId()) {
                        if (Objects.isNull(detail.getQuantity())) {
                            detail.setQuantity(addon.getQuantity());
                        }
                        if (Objects.isNull(detail.getUom())) {
                            detail.setUom(addon.getUom());
                        }
                        if (Objects.isNull(detail.getDimension())) {
                            detail.setDimension(addon.getDimension());
                        }
                    }
                }
            }
            if (Objects.isNull(detail.getQuantity()) && Objects.isNull(detail.getDimension())) {
                log.info("Null Addons not handled for product {} for recipe {}", detail.getId(), recipeDetail.getRecipeId());
                detail.setDimension(recipeDetail.getDimension());
                detail.setQuantity(BigDecimal.ONE);//TODO fix the code and remove this part.
            }
        }
    }

    private boolean handleNullAddonsAndVariants(IngredientVariantDetail detail, RecipeDetail recipeDetail) {
        if(Objects.nonNull(recipeDetail)) {
            if (Objects.nonNull(recipeDetail.getIngredient())
                    && !CollectionUtils.isEmpty(recipeDetail.getIngredient().getVariants())) {
                for (IngredientVariant variant : recipeDetail.getIngredient().getVariants()) {
                    if (!CollectionUtils.isEmpty(variant.getDetails())) {
                        for (IngredientVariantDetail ingredientVariantDetail : variant.getDetails()) {
                            if (ingredientVariantDetail.getProductId() == detail.getId() || (StringUtils.isNotBlank(ingredientVariantDetail.getName()) ? ingredientVariantDetail.getName().equals(detail.getName()) : ingredientVariantDetail.getAlias().equals(detail.getAlias()))) {
                                if (detail.getId() != ingredientVariantDetail.getProductId()) {
                                    log.info("Changing variant id from {} (from ui) to {} (found in reciepe) ", detail.getId(), ingredientVariantDetail.getProductId());
                                    detail.setProductId(ingredientVariantDetail.getProductId());
                                    detail.setId(ingredientVariantDetail.getProductId());
                                }
                                if (Objects.isNull(detail.getQuantity())) {
                                    detail.setQuantity(ingredientVariantDetail.getQuantity());
                                }
                                if (Objects.isNull(detail.getUom())) {
                                    detail.setUom(ingredientVariantDetail.getUom());
                                }
                            }
                        }
                    }
                }
            }
            if (Objects.isNull(detail.getQuantity()) || Objects.isNull(detail.getUom())) {
                log.info("Null Addons not handled for variant {} for recipe {}", detail.getId(), recipeDetail.getRecipeId());
                try {
                    log.info("Recipe data ::: {} ", (new JSONObject(recipeDetail)).toString());
                } catch (Exception e) {

                }
                return true;
            }
            return false;
        }else{
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderMetricDetailData createOrderMetricData(OrderMetricDomain orderMetricDomain){
        try {
            OrderMetricDetailData data = OrderMetricConverter.convertToOrderMetricDetailData(orderMetricDomain);
            Map<String,String> value = orderService.setVersionDetails(orderMetricDomain);
            if(Objects.nonNull(value)){
                data.setPosVersion(value.get(AppConstants.POS));
                data.setCafeAppVersion(value.get(AppConstants.CAFE_APP));
            }
            List<CustomerBrandMapping> mappings = customerBrandMappingDao.findByCustomerId(orderMetricDomain.getCustomerId());
            boolean isNewCustomer = false;
            if(!CollectionUtils.isEmpty(mappings)){
                for(CustomerBrandMapping mapping : mappings){
                    if(mapping.getTotalOrder() == 1 && mapping.getLastOrderId() == orderMetricDomain.getOrderMetricData().getOrderId()){
                        isNewCustomer = true;
                        break;
                    }
                    if(mapping.getTotalOrder() == 0 && mapping.getTotalSpecialOrder() == 1 && mapping.getLastSpecialOrderId() == orderMetricDomain.getOrderMetricData().getOrderId()){
                        isNewCustomer = true;
                        break;
                    }
                }
            }
            data.setNewCustomer(AppUtils.getYOrN(isNewCustomer));
            data.setFreeProductProgramInformed(Objects.nonNull(orderMetricDomain.isFreeProductProgramInformed()) ? AppUtils.getYOrN(orderMetricDomain.isFreeProductProgramInformed()) : null);
            orderMetricDao.save(data);
            return data;
        } catch (Exception e) {
            log.error("Error occurred while saving order metric data for order id {} :::: {}", orderMetricDomain.getOrderMetricData().getOrderId(), e);
            return null;
        }
    }

    private void saveOrderCountOfSavedChai(Order order, Integer orderId) {
        try {
            Map<Integer, Integer> itemPreferenceMap = new HashMap<>();
            List<CustomerFavChaiMapping> favChaiMappings = new ArrayList<>();
            List<CustomerFavChaiMapping> mappings = favChaiMappingDao.findByCustomerIdAndStatus(order.getCustomerId(), AppConstants.ACTIVE);
            if (Objects.nonNull(order.getOrders())) {
                order.getOrders().stream().filter(item -> Objects.nonNull(item.getPreferenceDetail())
                 && Objects.nonNull(item.getPreferenceDetail().getPreferenceId())).map(orderItem ->
                     itemPreferenceMap.put(orderItem.getPreferenceDetail().getPreferenceId(),orderItem.getQuantity())
                ).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(mappings)) {
                for (CustomerFavChaiMapping mapping : mappings) {
                    if (itemPreferenceMap.containsKey(mapping.getCustomizationId())) {
                        if(Objects.isNull(mapping.getTotalOrderCount())){
                            mapping.setTotalOrderCount(0);
                        }
                        mapping.setTotalOrderCount(mapping.getTotalOrderCount() + itemPreferenceMap.get(mapping.getCustomizationId()));
                        favChaiMappings.add(mapping);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(favChaiMappings)) {
                favChaiMappingDao.saveAll(favChaiMappings);
            }
        } catch (Exception e) {
            log.error("Error while saving the order count of saved chai for order id ::::: {}", orderId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void tableCheckout(TableSettlement settlement) throws DataUpdationException {
        UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId( settlement.getTableRequestId());
        if(TableStatus.CLOSED.name().equals(table.getTableStatus())) {
            throw new DataUpdationException("Table already closed");
        }

        try {

            OrderDomain walletOrderDomain = settlement.getWalletOrderDomain();
            String randomString = walletOrderDomain.getOrder().getGenerateOrderId();
            OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
            WalletOrder wallet = walletOrderDomain.getWalletOrder();
            Order order = validateOrder(walletOrderDomain, orderUnitMapping);
            if(validateWalletOrder(wallet, order)){
                OrderDomain orderDomain = new OrderDomain();
                orderDomain.setOrder(order);
                Order walletOrder = extractWalletOrder(order, WalletOrderType.MICRO, wallet);
                settlement.setSettlements(order.getSettlements());
                OrderInfo info = null;
                info = processPlainOrder(walletOrder, walletOrderDomain.isIncludeReceipts(), walletOrderDomain.isAddMetadata(),
                        walletOrderDomain.getOrderNotification(), randomString, orderUnitMapping);
            }
        } catch (OfferValidationException | CardValidationException | DataNotFoundException |
                 TemplateRenderingException | JMSException e) {
            throw new RuntimeException(e);
        }
        processSettlements(table, settlement, true);
        processSettlements(table, settlement, false);
        checkSettlementIsComplete(settlement);
        //closed as flow not working correctly
        //awardLoyalty(table);
        table.setTableStatus(TableStatus.CLOSED.name());
        tableMappingDetailDao.save(table);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void tableCheckoutNew(TableSettlement settlement) throws DataUpdationException {
        UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId( settlement.getTableRequestId());
        if(TableStatus.CLOSED.name().equals(table.getTableStatus())) {
            throw new DataUpdationException("Table already closed");
        }
        OrderDetail settledOrder = null;
        if(Objects.nonNull(table.getSettledOrderId())){
            settledOrder = orderDetailDao.findByOrderId(table.getSettledOrderId());
        }
        try {
            if(Objects.nonNull(settlement.getWalletOrderDomain())) {
                OrderDomain walletOrderDomain = settlement.getWalletOrderDomain();
                String randomString = walletOrderDomain.getOrder().getGenerateOrderId();
                OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
                WalletOrder wallet = walletOrderDomain.getWalletOrder();
                Order order = validateOrder(walletOrderDomain, orderUnitMapping);
                if (validateWalletOrder(wallet, order)) {
                    OrderDomain orderDomain = new OrderDomain();
                    orderDomain.setOrder(order);
                    Order walletOrder = extractWalletOrder(order, WalletOrderType.MICRO, wallet);
                    settlement.setSettlements(order.getSettlements());
                    OrderInfo info = null;
                    info = processPlainOrder(walletOrder, walletOrderDomain.isIncludeReceipts(), walletOrderDomain.isAddMetadata(),
                            walletOrderDomain.getOrderNotification(), randomString, orderUnitMapping);
                }
            }
        } catch (OfferValidationException | CardValidationException | DataNotFoundException |
                 TemplateRenderingException | JMSException e) {
            throw new RuntimeException(e);
        }
        if(Objects.nonNull(settledOrder)) {
            processSettlementsNew(settledOrder, settlement, true);
            processSettlementsNew(settledOrder, settlement, false);
            checkSettlementIsComplete(settlement);
            table.setTableStatus(TableStatus.CLOSED.name());
            tableMappingDetailDao.save(table);
        }else{
            processSettlements(table, settlement, true);
            processSettlements(table, settlement, false);
            checkSettlementIsComplete(settlement);
            table.setTableStatus(TableStatus.CLOSED.name());
            tableMappingDetailDao.save(table);
        }
    }

    private void checkSettlementIsComplete(TableSettlement settlement) throws DataUpdationException {
        for (Settlement s : settlement.getSettlements()) {
            if (s.getAmount().intValue() > 1) {
                throw new DataUpdationException("Settlement Amount does not match.");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void publishAllOrders(List<Integer> orderIdList) {
        for (Integer orderId : orderIdList) {
            try {
                OrderInfo info = getOrderInfo(getOrderDetail(orderId), false, null);
                publish(info);
            } catch (Exception e) {
                log.error("Error while publishing customer", e);
            }
        }
    }

    private void processSettlements(UnitTableMappingDetail table, TableSettlement settlement, boolean giftCardOrders)
            throws DataUpdationException {
        List<Settlement> settlementList = null;
        for (TableOrderMappingDetail orderMap : table.getOrders()) {
            if (TransactionUtils.isCancelled(orderMap.getOrder().getOrderStatus())) {
                // skip cancelled orders as amount is not distributed here
                continue;
            }
            if (giftCardOrders && !AppConstants.getValue(orderMap.getOrder().getGiftCardOrder())) {
                // processing gift cards only
                continue;
            }
            if (!giftCardOrders && AppConstants.getValue(orderMap.getOrder().getGiftCardOrder())) {
                // processing non gift cards only
                continue;
            }
            settlementList = new ArrayList<>();
            BigDecimal paidAmount = orderMap.getOrder().getSettledAmount();

            for (Settlement s : settlement.getSettlements()) {
                if (giftCardOrders && s.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
                    // skip settlement by gift card when order is gift card
                    continue;
                }
                if (s.getAmount().intValue() > 0 && paidAmount.intValue() > 0) {
                    if (paidAmount.intValue() <= s.getAmount().intValue()) {
                        s.setAmount(s.getAmount().subtract(paidAmount));
                        addToMap(orderMap.getOrder(), s, paidAmount, settlementList);
                        paidAmount = BigDecimal.ZERO;
                    } else {
                        paidAmount = paidAmount.subtract(s.getAmount());
                        addToMap(orderMap.getOrder(), s, s.getAmount(), settlementList);
                        s.setAmount(BigDecimal.ZERO);
                    }
                }
            }

            if (settlementList.isEmpty() && paidAmount.intValue() == 0) {
                // for orders with Zero amount
                Settlement s = new Settlement();
                s.setAmount(BigDecimal.ZERO);
                s.setMode(AppConstants.PAYMENT_MODE_CASH);
                s.setModeDetail(unitCacheService.getPaymentModeById(AppConstants.PAYMENT_MODE_CASH));
                settlementList.add(s);
            }

            int i = 0;
            for (Settlement s : settlementList) {
                if (i == 0) {
                    if (orderMap.getOrder().getOrderSettlements() != null
                            && !orderMap.getOrder().getOrderSettlements().isEmpty()) {
                        OrderSettlement os = orderMap.getOrder().getOrderSettlements().get(0);
                        addSettlement(orderMap.getOrder(), s, os);
                    }
                    i++;
                    continue;
                }
                addSettlement(orderMap.getOrder(), s, null);

            }
        }
    }


    private void processSettlementsNew(OrderDetail order, TableSettlement settlement, boolean giftCardOrders)
            throws DataUpdationException {
        List<Settlement> settlementList = null;
        if (TransactionUtils.isCancelled(order.getOrderStatus()) ||
                (giftCardOrders && !AppConstants.getValue(order.getGiftCardOrder())) ||
                (!giftCardOrders && AppConstants.getValue(order.getGiftCardOrder()))) {
            // skip cancelled orders as amount is not distributed here
            return;
        }
        settlementList = new ArrayList<>();
        BigDecimal paidAmount = order.getSettledAmount();

        for (Settlement s : settlement.getSettlements()) {
            if (giftCardOrders && s.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
                // skip settlement by gift card when order is gift card
                continue;
            }
            if (s.getAmount().intValue() > 0 && paidAmount.intValue() > 0) {
                if (paidAmount.intValue() <= s.getAmount().intValue()) {
                    s.setAmount(s.getAmount().subtract(paidAmount));
                    addToMap(order, s, paidAmount, settlementList);
                    paidAmount = BigDecimal.ZERO;
                } else {
                    paidAmount = paidAmount.subtract(s.getAmount());
                    addToMap(order, s, s.getAmount(), settlementList);
                    s.setAmount(BigDecimal.ZERO);
                }
            }
        }

        if (settlementList.isEmpty() && paidAmount.intValue() == 0) {
            // for orders with Zero amount
            Settlement s = new Settlement();
            s.setAmount(BigDecimal.ZERO);
            s.setMode(AppConstants.PAYMENT_MODE_CASH);
            s.setModeDetail(unitCacheService.getPaymentModeById(AppConstants.PAYMENT_MODE_CASH));
            settlementList.add(s);
        }

        int i = 0;
        for (Settlement s : settlementList) {
            if (i == 0) {
                if (order.getOrderSettlements() != null
                        && !order.getOrderSettlements().isEmpty()) {
                    OrderSettlement os = order.getOrderSettlements().get(0);
                    addTableSettlement(order, s, os);
                }
                i++;
                continue;
            }
            addTableSettlement(order, s, null);
        }
    }

    private void addToMap(OrderDetail orderDetail, Settlement s, BigDecimal amount, List<Settlement> settlementList) {
        Settlement s1 = new Settlement();
        s1.setAmount(amount);
        s1.setMode(s.getMode());
        s1.setModeDetail(s.getModeDetail());
        BigDecimal currentSettlement = BigDecimal.valueOf(amount.doubleValue());
        if (!s.getExternalSettlements().isEmpty()) {
            for (ExternalSettlement es : s.getExternalSettlements()) {
                if (currentSettlement.intValue() > 0 && es.getAmount().intValue() > 0) {
                    if (currentSettlement.intValue() <= es.getAmount().intValue()) {
                        s1.getExternalSettlements().add(new ExternalSettlement(es.getExternalSettlementId(),
                                currentSettlement, es.getExternalTransactionId()));
                        es.setAmount(es.getAmount().subtract(currentSettlement));
                        currentSettlement = BigDecimal.ZERO;
                    } else {
                        s1.getExternalSettlements().add(new ExternalSettlement(es.getExternalSettlementId(),
                                es.getAmount(), es.getExternalTransactionId()));
                        currentSettlement = currentSettlement.subtract(es.getAmount());
                        es.setAmount(BigDecimal.ZERO);
                    }
                }
            }
        }
        settlementList.add(s1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public Boolean deductWalletAmountForOrders(List<Integer> orderIds) {
        // now first looping on orders and getting the order and settlement id for that order
        for (Integer orderId : orderIds) {
            log.info("processing wallet deduction for order {} " , orderId);
            OrderDetail orderDetail = orderDetailDao.findByOrderId(orderId);

            BigDecimal amountUsedUsingWallet = BigDecimal.ZERO;
            OrderSettlement orderSettlement = null;
            for (OrderSettlement orderSettlementEntry : orderDetail.getOrderSettlements()) {
                if (orderSettlementEntry.getPaymentModeId() == 10) {
                    // this is a wallet order
                    orderSettlement = orderSettlementEntry;
                    amountUsedUsingWallet = orderSettlement.getAmountPaid();
                    break;
                }
            }
            if (Objects.isNull(orderSettlement)) {
                log.info("Not Used Wallet For order {} and customer id : {} " , orderId, orderDetail.getCustomerId());
                continue;
            } else {
                log.info("Used Wallet For order {} and customer id : {} " , orderId, orderDetail.getCustomerId());
            }

            List<CashCardEvent> cashCardEventsByOrderId = cashCardEventDao.findByOrderId(orderId);

            BigDecimal alreadyDeductedAmount = BigDecimal.ZERO;
            if (Objects.nonNull(cashCardEventsByOrderId)) {
                for (CashCardEvent l : cashCardEventsByOrderId) {
                    alreadyDeductedAmount = alreadyDeductedAmount.add(l.getSettlementAmount());
                }
            }

            if (CollectionUtils.isEmpty(cashCardEventsByOrderId) || (amountUsedUsingWallet.compareTo(alreadyDeductedAmount) != 0)) {
                amountUsedUsingWallet = amountUsedUsingWallet.subtract(alreadyDeductedAmount);
                log.info("No cash card events found for order {} and customer id : {} " , orderId, orderDetail.getCustomerId());
                // getting all the cash cards and deducting amount till what ever we can

                List<CashCardDetail> cashCards = cashCardDetailDao.findAvailableCashCards(orderDetail.getCustomerId(),
                        AppConstants.ACTIVE);
                BigDecimal pendingAmount = new BigDecimal(amountUsedUsingWallet.intValue());
                for (int i =0 ;i <cashCards.size() ;i++) {
                    CashCardDetail cashCard = cashCards.get(i);
                    if (cashCard.getCardStatus().equals(CashCardStatus.READY_FOR_ACTIVATION.name())) {
                        cashCard.setCardStatus(CashCardStatus.ACTIVE.name());
                    }
                    if(pendingAmount.compareTo(BigDecimal.ZERO) == 1){
                        BigDecimal deductionAmount = cashCard.getCashPendingAmount().compareTo(pendingAmount) >= 0 ? pendingAmount
                                : cashCard.getCashPendingAmount();

                        cashCard.setCashPendingAmount(cashCard.getCashPendingAmount().subtract(deductionAmount));
                        cashCard.setLastModified(AppUtils.getCurrentTimestamp());

                        cashCardDetailDao.save(cashCard);

                        CashCardEvent event = new CashCardEvent();
                        event.setCashCardId(cashCard.getCashCardId());
                        event.setOrderId(orderId);
                        event.setOrderSettlementId(orderSettlement.getSettlementId());
                        event.setSettlementAmount(deductionAmount);
                        event.setSettlementStatus(AppConstants.ACTIVE);
                        event.setSettlementTime(AppUtils.getCurrentTimestamp());
                        cashCardEventDao.save(event);

                        pendingAmount = pendingAmount.subtract(deductionAmount);

                    }else if(pendingAmount.compareTo(BigDecimal.ZERO) == 0){
                        break;
                    }
                }
                CashCardCorrectionLog cashCardCorrectionLog = new CashCardCorrectionLog();
                cashCardCorrectionLog.setOrderId(orderId);
                cashCardCorrectionLog.setAmountToBeDeducted(amountUsedUsingWallet);
                cashCardCorrectionLog.setAmountDeducted(amountUsedUsingWallet.subtract(pendingAmount));
                cashCardCorrectionLog.setAmountLeftForDeduction(pendingAmount);
                cashCardCorrectionLog.setCreationTime(AppUtils.getCurrentTimestamp());
                cashCardCorrectionLogDao.save(cashCardCorrectionLog);
            } else {
                log.info("cash card events are already created for order {} and customer id : {} ignoring for this customer" , orderId, orderDetail.getCustomerId());
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    private void createCashCardEvents(OrderDetail order,Settlement settlement,Integer settlementId) throws DataUpdationException {
        settlement.setExternalSettlements(new ArrayList());

        List<CashCardDetail> cashCards = cashCardDetailDao.findAvailableCashCards(order.getCustomerId(),
                AppConstants.ACTIVE);
        BigDecimal pendingAmount = settlement.getAmount();
        for (int i =0 ;i <cashCards.size() ;i++) {
            CashCardDetail cashCard = cashCards.get(i);
            if (cashCard.getCardStatus().equals(CashCardStatus.READY_FOR_ACTIVATION.name())) {
                cashCard.setCardStatus(CashCardStatus.ACTIVE.name());
            }
            if(pendingAmount.compareTo(BigDecimal.ZERO) == 1){
                BigDecimal deductionAmount = cashCard.getCashPendingAmount().compareTo(pendingAmount) >= 0 ? pendingAmount
                        : cashCard.getCashPendingAmount();

                cashCard.setCashPendingAmount(cashCard.getCashPendingAmount().subtract(deductionAmount));
                cashCard.setLastModified(AppUtils.getCurrentTimestamp());

                cashCardDetailDao.save(cashCard);

                CashCardEvent event = new CashCardEvent();
                event.setCashCardId(cashCard.getCashCardId());
                event.setOrderId(order.getOrderId());
                event.setOrderSettlementId(settlementId);
                event.setSettlementAmount(deductionAmount);
                event.setSettlementStatus(AppConstants.ACTIVE);
                event.setSettlementTime(AppUtils.getCurrentTimestamp());
//	        event.setOpeningBalance(AppUtils.add(amount,detail.getCashPendingAmount()));
//	        event.setClosingBalance(detail.getCashPendingAmount());
                cashCardEventDao.save(event);

                pendingAmount = pendingAmount.subtract(deductionAmount);
                settlement.getExternalSettlements().add(new ExternalSettlement(0, deductionAmount, cashCard.getCashCardId().toString()));

            }else if(pendingAmount.compareTo(BigDecimal.ZERO) == 0){
                settlement.getExternalSettlements()
                        .add(new ExternalSettlement(0, BigDecimal.ZERO, cashCard.getCashCardId().toString()));
                break;
            }
        }
        if(pendingAmount.compareTo(BigDecimal.ZERO) == 1){
                log.info("########## Customer Don't have enough Wallet Balance for this order");
                throw new DataUpdationException("Customer Don't have enough  Wallet Balance for this order");
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void createOrderMetricData(WebOrderMetricDomain orderMetricDomain){
        try {
            log.info("WEB ORDER METRIC DATA FOR"+ new Gson().toJson(orderMetricDomain));
            WebOrderMetricDetailData data = OrderMetricConverter.convertToWebOrderMetricDetailData(orderMetricDomain);
            webOrderMetricDao.save(data);

        } catch (Exception e) {
            log.error("Error occurred while saving order metric data for order id {} ::::", orderMetricDomain.getOrderId(), e);

        }
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public TableViewOrder createTableOrder(TableViewOrder tableViewOrder) throws DataUpdationException, DataNotFoundException, JMSException, TemplateRenderingException, CardValidationException {
        StringBuilder sb = new StringBuilder();
        Order order = Objects.nonNull(tableViewOrder.getOrder()) ? tableViewOrder.getOrder() : null;
        UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId( tableViewOrder.getOrder().getTableRequestId());
        long time = System.currentTimeMillis();
        Date currentTimestamp = AppUtils.getCurrentTimestamp();
        OrderUnitMapping orderUnitMapping  = new OrderUnitMapping();
        OrderDetail orderDetail;
        validateTableOrder(tableViewOrder.getOrder());
        time = System.currentTimeMillis();
        UnitSessionDetail session = unitSessionCache
                .get(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
        if(Objects.nonNull(table.getGeneratedOrderId())){
            order.setGenerateOrderId(table.getGeneratedOrderId());
        }else {
            order.setGenerateOrderId(session.getGeneratedOrderId());
        }
        SubscriptionProduct subscriptionProduct = subscriptionPlanService.getSubscriptionProduct(order);
        boolean createSubscription = false;
        Customer customer = null;
        if(Objects.isNull(order.getCustomerId())){
            order.setCustomerId(5);
        }
        if (subscriptionProduct != null) {
            try {
                customer = customerService.getCustomer(order.getCustomerId());
                if (Objects.isNull(customer)) {
                    throw new DataNotFoundException();
                }else{
                    if(StringUtils.isBlank(customer.getFirstName())){
                        customer.setFirstName(order.getCustomerName());
                        customerService.updateCustomerName(customer);
                    }
                }
            } catch (DataNotFoundException e) {
                unitSessionCache.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
                throw new DataUpdationException("Unable to find customer with id " + order.getCustomerId());
            }
            List<String> errors = subscriptionPlanService.validateSubscriptionRequest(order, customer, null);
            if (!CollectionUtils.isEmpty(errors)) {
                String error = String.format("Errors while punching the subscription order:\n %s",
                        StringUtils.join(errors, ",\n"));
                log.error(error);
                throw new DataUpdationException(error);
            } else {
                log.info("Got the request to purchase subscription for order id {} and customer id {}",
                        order.getGenerateOrderId(), order.getCustomerId());
                createSubscription = true;
            }
        }

        order = preConsolidatedTableOrderCreation(order,sb);

        sb.append("\n########## , STEP A, - , Check for subscription purchase request ----------,"
                + (System.currentTimeMillis() - time));

        String orderFeedbackType = properties.getOrderFeedbackType();
        Brand brand = brandMetaDataCache.getBrandMetaData().get(order.getBrandId());
        Unit unit = null;
        try {
            unit = unitCacheService.getUnitById(order.getUnitId());
            time = System.currentTimeMillis();
            orderDetail = addTableOrder(unit, order, currentTimestamp, table,sb);
            sb.append("\n########## , STEP A, - , Add Order ----------," + (System.currentTimeMillis() - time));
        } catch (Exception e) {
            unitSessionCache.generateToken(new UnitTerminalDetail(order.getUnitId(), order.getTerminalId()));
            throw e;
        }
        time = System.currentTimeMillis();
        enquiryItemService.addOrderEnquiryItems(order, orderDetail.getOrderId());
        sb.append("\n########## , STEP B, - , Add Enquiry Items ----------," + (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();
        orderMetadataService.addMetadataDetails(order, orderDetail.getOrderId());
        sb.append("\n########## , STEP C, - , Add Metadata Details ----------," + (System.currentTimeMillis() - time));
        CreateOrderResult result = new CreateOrderResult();
        result.setOrderId(orderDetail.getOrderId());
        result.setFeedbackId(-1);
        result.setGenerateQRCode(false);
        result.setGenerateInAppFeedback(false);
        try {
            if (order.getCustomerId() > 5 && !TransactionUtils.isSpecialOrder(order) && !isSubscriptionOrder(order)) {
                Set<Integer> productIds = getProductIds(order);
                if (Objects.isNull(customer)) {
                    time = System.currentTimeMillis();
                    customer = customerService.getCustomer(order.getCustomerId());
                    if (Objects.isNull(customer)) {
                        throw new DataNotFoundException();
                    } else{
                        if(StringUtils.isBlank(customer.getFirstName())){
                            customer.setFirstName(order.getCustomerName());
                            customerService.updateCustomerName(customer);
                        }
                    }
                    sb.append("\n########## , STEP D, - , Customer Lookup ----------,"
                            + (System.currentTimeMillis() - time));
                }

                if (order.getBrandId() == 1 && !customer.isChaayosCustomer()) {
                    // set is chaayos customer flag to true
                    customerDao.updateIsChaayosCustomer(customer.getId(), AppUtils.YES);
                }
//                validateOrder(orderDetail, orderUnitMapping);
                // check if loyalty is allowed for brand
                // ---------------------------------------------------------------------------------------------------------------------------
                time = System.currentTimeMillis();
                if (brand.getAwardLoyalty() && Objects.nonNull(order.getTransactionDetail()) && !order.isSkipLoyaltyProducts()) {
                    if(order.isBypassLoyateaAward()){
                        order.setOrderId(orderDetail.getOrderId());
                        loyaltyService.handleFailedLoyaltyAwardOrder(order, LoyaltyFailedReason.COUPON_REDEEMED.name(),!productIds.isEmpty());
                    }
                    else if(Objects.nonNull(order.getCashBackReceived()) && order.getCashBackReceived() && properties.getLoyaltyFlagForCashBack()){
                        order.setOrderId(orderDetail.getOrderId());
                        loyaltyService.handleFailedLoyaltyAwardOrder(order, LoyaltyFailedReason.CASHBACK_AWARDED.name(),!productIds.isEmpty());
                    }
                    else {
                        loyaltyService.awardloyalty(productIds, order, customer, orderDetail, order.isForceAwardLoyalty());
                        sb.append("\n########## , STEP E.1, - , Customer Update Loyalty Award----------,"
                                + (System.currentTimeMillis() - time));
                    }
                } else {
                    sb.append("\n########## , STEP E.1, - ,Skipping Customer Update Loyalty Award---------- ,"
                            + brand.getBrandName() + " " + (System.currentTimeMillis() - time));
                }
                if(order.isSkipLoyaltyProducts()){
                    offerManagementService.removeSecondFreeChai(order.getCustomerId(), SignupOfferStatus.EXPIRED.name());
                }
                if (brand.getAwardLoyalty() ) {
                    if (order.getPointsRedeemed() != 0) {
                        time = System.currentTimeMillis();
                        boolean haveValidLoyaltyPoints;
                        if(Objects.nonNull(order.getOfferAccountType()) && AppConstants.LOYALTY_OFFER.equals(order.getOfferAccountType()) &&
                                Objects.nonNull(order.getPointsRedeemed())
                                && order.getPointsRedeemed() < 0){
                            haveValidLoyaltyPoints = loyaltyService.updateScore(customer.getId(),
                                    LoyaltyEventType.LOYALTY_OFFER,order.getPointsRedeemed(),
                                    orderDetail.getOrderId(),false,productIds.size()>0);
                        }else {
                            haveValidLoyaltyPoints = loyaltyService.updateScore(customer.getId(),
                                    LoyaltyEventType.REGULAR_REDEMPTION, order.getPointsRedeemed(),
                                    orderDetail.getOrderId(), false, productIds.size() > 0);
                        }
                        if (!haveValidLoyaltyPoints) {
                            log.info("########## Customer Don't have enough Loyalty Points for this order");
                            throw new DataUpdationException("Customer Don't have enough Loyalty Points for this order");
                        }
                        sb.append("\n########## , STEP E.2, - , Customer Update Loyalty Redemption----------,"
                                + (System.currentTimeMillis() - time));

                    } else {
                        sb.append("\n########## , STEP E.2, - ,Skipping Customer Update Loyalty Redemption---------- ,"
                                + brand.getBrandName());
                    }
                    log.info("Should NPS be sent for + " + brand.getBrandName() + " ? " + brand.getSendNPS());
                    if (brand.getSendNPS()) {
                        feedbackManagementService.createNPSFeedBack(productIds, customer, orderDetail, currentTimestamp,
                                    orderFeedbackType, result, order, sb);
                    } else {
                        log.error("NPS Skipped for brand " + brand.getBrandName());
                    }


                    notificationService.triggerEmail(brand, orderDetail, order, customer, currentTimestamp, sb);

                } else {
                    time = System.currentTimeMillis();
                    if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(orderDetail.getCustomerId())
                            && !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
                        notificationService.generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(),
                                1, properties.getUndeliveredEmail(), true, true, currentTimestamp, null);
                    }
                    sb.append("\n########## , STEP H, - , Customer Email Event Creation 1----------,"
                            + (System.currentTimeMillis() - time));
                    int reasonId = -1;
                    for (OrderItem item : order.getOrders()) {
                        if (item.getComplimentaryDetail().isIsComplimentary()
                                && item.getComplimentaryDetail().getReasonCode() != null
                                && item.getComplimentaryDetail()
                                .getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
                            reasonId = item.getComplimentaryDetail().getReasonCode();
                            break;
                        }
                    }
                    if (reasonId != -1 && reasonId == 2107) {
                        notificationService.sendCharityOrderNotification(orderDetail, currentTimestamp);
                    }
                }
            }
        } catch (DataNotFoundException e) {
            if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
                    && !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
                notificationService.generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1,
                        null, true, currentTimestamp);
            }
        }
        Set<Integer> paymentIdSet = null;
        paymentGatewayService.processOrderPayment(order, paymentIdSet, orderDetail, sb);

        if (order.getOptionResultEventId() != null) {
            time = System.currentTimeMillis();
            rulesEventService.attach(order.getOptionResultEventId(), orderDetail.getOrderId());
            sb.append("\n########## , STEP J, - , Time Taken To Link The Recommendation----------,"
                    + (System.currentTimeMillis() - time));
        }

        if (TransactionUtils.isTableOrder(order)) {
            if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), order.getOrders().get(0).getCardType())) {
                // do not attach table for gyftr gift card orders
                orderDetail.setTableNumber(null);
                orderDetail.setTableRequestId(null);
            }
        }

        cashBackService.processCashRedemption(order, orderDetail, sb);

        if (Objects.nonNull(customer) && !customer.isReferrerAwarded() && Objects.nonNull(customer.getReferrerId())) {
            // activate referral on transaction
            time = System.currentTimeMillis();
            cashPacketService.activateReferralCashPacket(order.getCustomerId(), customer.getReferrerId(),
                    orderDetail.getOrderId());
            sb.append("\n########## , STEP K, - , Time Taken To activate cash referral----------,"
                    + (System.currentTimeMillis() - time));
        }

        List<String> newCards = new ArrayList<>();
        for (OrderItem item : order.getOrders()) {
            if (item.getCardType() != null && (CashCardType.ECARD.name().equalsIgnoreCase(item.getCardType()))
                    || voucherManagementService.isGyftrCard(item.getCardType())) {
                newCards.add(item.getItemCode());
            }
        }
        if (createSubscription) {
            time = System.currentTimeMillis();
            SubscriptionPlan plan = subscriptionPlanService.createSubscription(orderDetail, subscriptionProduct,
                    customer);
            sb.append("\n########## , STEP L, - , Create subscription purchase request ----------,"
                    + (System.currentTimeMillis() - time));
            result.setSubscriptionPlan(plan);
        }
        result.setGiftCard(newCards);
        OrderNotification orderNotification = new OrderNotification();
        OrderInfo info = postConsolidatedTableOrderCreation(order, result, true, true, orderNotification, sb,orderDetail.getGeneratedOrderId()
                , orderUnitMapping);
        table.setSettledOrderId(info.getOrder().getOrderId());
        if(Objects.nonNull(order.getTransactionDetail().getServiceCharge())) {
            table.setTableStatus(TableStatus.CLOSED.name());
        }
        if(Objects.nonNull(table.getSettledOrderId()) && Objects.nonNull(table.getOrderStartTime())){
            table.setTableSettlementTime(AppUtils.getCurrentTimestamp());
            table.setTotalOrderTimeInMin(AppUtils.
                    getMinDiffernce(table.getOrderStartTime(),table.getTableSettlementTime()));
        }
        tableMappingDetailDao.save(table);
        Order resultOrder =  convertOrder(orderDetail,null);
        tableViewOrder.setOrder(resultOrder);
        tableViewOrder.setOrderInfo(info);
        markTableOrder(tableViewOrder.getTableOrderIds());
        unitSessionCache.generateToken(new UnitTerminalDetail(session.getUnitId(), session.getTerminalId()));
        log.info(sb);
        if(Objects.nonNull(order.getDreamFolksVoucherDetails()) && Objects.nonNull(order.getDreamFolksVoucherDetails().getVoucherCode())){
            updateDreamFolksOrderIdByVoucherCode(order.getDreamFolksVoucherDetails().getVoucherCode(), tableViewOrder.getOrder().getOrderId());
        }
        return tableViewOrder;
    }


    private void validateTableOrder(Order order) throws DataUpdationException {
        // validate settlement of order
        List<Settlement> settlements = order.getSettlements();
        if(!CollectionUtils.isEmpty(settlements)) {
            for (Settlement s : settlements) {
                if (Objects.isNull(s) || s.getMode() == 0) {
                    throw new DataUpdationException("Unable to Place order as settlement mode is 0");
                }
            }
        }else{
            throw new DataUpdationException("Unable to Place order as settlement null or empty");
        }
    }


    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void setFeedbackUrl(TableViewOrder tableViewOrder,TableResponse tableResponse){
        OrderInfo info = tableViewOrder.getOrderInfo();
        try {
            info.setFeedbackUrl(feedbackManagementService.getFeedbackUrl(info,tableResponse));
        } catch (Exception e) {
            log.info("Error in generating feedback url : {}",e.getMessage());
        }
        tableViewOrder.setOrderInfo(info);
    }

    private void markTableOrder(List<Integer> orderIds){
      List<OrderDetail> orderDetails =   orderDetailDao.findAllById(orderIds);
      for(OrderDetail orderDetail :  orderDetails){
          orderDetail.setOrderType(AppConstants.ORDER_TYPE_TABLE_ORDER);
      }
    }

    private OrderInfo postConsolidatedTableOrderCreation(Order order, CreateOrderResult result, boolean includeReceipts,
                                             boolean addMetadata, OrderNotification orderNotification, StringBuilder sb,
                                             String randonOrderGeneratedString, OrderUnitMapping orderUnitMapping)
            throws DataNotFoundException, TemplateRenderingException, JMSException {
        long startTime = System.currentTimeMillis();
        boolean newCustomer = order.isNewCustomer();

        OrderInfo info = generateAndSaveMetadataPostOrderCreation(order, result, includeReceipts, newCustomer, sb);

        updateMetadataPostOrderCreation(order, result, info, sb);

        sendNotificationsPostOrderCreation(unitCacheService.getUnitById(order.getUnitId()), order, result, info,
                orderNotification, sb);


        startTime = System.currentTimeMillis();
        sb.append(String.format("\n----------- ,STEP 12, - ,Book Wastage, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));
        // 13
        startTime = System.currentTimeMillis();
        // allot cashback
        allotCashBack(order, result, info, orderNotification);
        sb.append(String.format("\n----------- ,STEP 13, - ,Alot Cashback, ---------------- ,%d, milliseconds ",
                System.currentTimeMillis() - startTime));

        // TODO Allot NBO and DNBO
        offerManagementService.createNBOandDNBOOffers(order, result, info, sb);

        if (addMetadata) {
            info.getOrder().setMetadataList(order.getMetadataList());
        }

        if (!properties.getSystemGeneratedNotifications()) {
            // set order related metadata in orderNotification like unitName , customerName
            // etc
            getOrderMetadataNotificationPayload(info, orderNotification);
        }

        if (Objects.nonNull(order.getPointsAcquired())) {
            log.info("Checking if acquired points are set in order for orderId ::{} and points are::{} ",
                    result.getOrderId(), order.getPointsAcquired());
        }

        orderMappingCache.setGeneratedOrderId(orderUnitMapping, randonOrderGeneratedString,
                info.getOrder().getGenerateOrderId());
        /*
         * if (Objects.nonNull(order.getPointsAcquired()) && order.getPointsAcquired() >
         * 0 && Objects.nonNull(info.getCustomer().getOptWhatsapp()) &&
         * info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)){
         * sendLoyalTeaWhatsappNotification(order,info,orderNotification); }
         */
        boolean hasSelectOrder = hasSelectOrder(order);

        sendLoyalTeaNotifications(order, info, orderNotification, hasSelectOrder);

        info.setOrderNotification(orderNotification);
        saveOrderCountOfSavedChai(order,info.getOrder().getOrderId());
        // log.info("Printing order Notification Payload for orderId ::{}
        // :::{}",order.getOrderId(),new Gson().toJson(orderNotification));
        return info;
    }

    private Order preConsolidatedTableOrderCreation(Order order, StringBuilder sb)
            throws CardValidationException, DataNotFoundException, DataUpdationException {
        order.setWhatsappNotificationPayloadType(AppConstants.ORDER_TYPE_REGULAR.toUpperCase());
        long startTime = System.currentTimeMillis();
        boolean newCustomer = order.isNewCustomer();

        Unit unitData = unitCacheService.getUnitById((order.getUnitId()));

        if(Boolean.TRUE.equals(unitData.getTestingUnit())) {
            order.setOrderType(AppConstants.ORDER_TYPE_TESTING_ORDER);
        } else if (StringUtils.isBlank(order.getOrderType())) {
            order.setOrderType(AppConstants.ORDER_TYPE_REGULAR);
        }



        couponService.applyLoyaltyCode(order);

        cashBackService.checkCashBack(order);

        cashBackService.awardCashBackOffer(order);

        validateGiftCardAndSubscription(order);
        // SETTING order status before creating database entry
        order.setStatus(OrderStatus.SETTLED);

        if (order.getOrders().get(0).getProductId() != 3) {
            order = applyFreeItemOffer(newCustomer, order);
        }

        return order;
    }

    public TableResponse getTableOrderRecipt(OrderDomain orderDomain,String serviceChargeApplied){
        OrderInfo orderInfo = getOrderInfoForTableOrderRecipt(orderDomain.getOrder(),true,null);
        String isGeneratedOrderId = AppConstants.NO;
        if(Objects.isNull(orderDomain.getOrder().getGenerateOrderId())) {
            UnitSessionDetail unitSessionDetail = unitSessionCache.get(new UnitTerminalDetail(orderDomain.getOrder().getUnitId(), orderDomain.getOrder().getTerminalId()));
            orderInfo.getOrder().setGenerateOrderId("T"+unitSessionDetail.getGeneratedOrderId());
            unitSessionCache.generateToken(new UnitTerminalDetail(orderDomain.getOrder().getUnitId(), orderDomain.getOrder().getTerminalId()));
            isGeneratedOrderId = AppConstants.YES;
        }
        orderInfo = generateTableOrderReceipt(orderInfo);
        TableResponse t = new TableResponse();
        if (Objects.nonNull(orderInfo) && !org.apache.commons.collections4.CollectionUtils.isEmpty(orderInfo.getReceipts())) {
            t.setSettlementReceipt(orderInfo.getReceipts().get(0));
        }
        tableDataService.updateTableDataForBillPrint(orderDomain.getOrder(),serviceChargeApplied, isGeneratedOrderId);
        addDreamFolksTransactionDetail(orderInfo,orderDomain.getOrder().getDreamFolksVoucherDetails());
        return t;
    }

    public OrderInfo generateTableOrderReceipt(OrderInfo info){
        long time = System.currentTimeMillis();
        Order order = info.getOrder();
        boolean needsCafeOrderPrint = !TransactionUtils.isSpecialOrder(order)
                || TransactionUtils.isPaidEmployeeMeal(order);
        Map<ReceiptType, String> androidReceipts;
        try {
            androidReceipts = TransactionUtils.getTableReceipts(properties.getChaayosBaseUrl(), needsCafeOrderPrint, info,
                    properties.getBasePath(), properties.getRawPrintingSatus(), properties);
            info.setAndroidReceipts(androidReceipts);
            List<String> receipts = new ArrayList<>();
            for (ReceiptType r : androidReceipts.keySet()) {
                receipts.add(androidReceipts.get(r));
            }
            info.setReceipts(receipts);
        }catch (TemplateRenderingException e) {
            log.error("Unable to render reciept Template", e);
        } catch (DataNotFoundException e) {
            log.error("exception occured while generating reciept", e);
        }
        if (properties.getRawPrintingSatus()) {
            info.setPrintType(PrintType.RAW);
        } else {
            info.setPrintType(PrintType.HTML);
        }

        log.info("get order info in reciept took {} ", System.currentTimeMillis() - time);
        return info;
    }

    public OrderInfo getOrderInfoForTableOrderRecipt(Order order, boolean includeReceipts, String customerName) {
        long time = System.currentTimeMillis();
        Unit unit = unitCacheService.getUnitById(order.getUnitId());
        Customer customer = null;
        if(Objects.nonNull(order.getCustomerId())) {
            customer = customerService.getCustomer(order.getCustomerId());
        }
        if (Objects.nonNull(customer) && StringUtils.isBlank(customer.getEmailId())) {
            customer.setEmailId(properties.getUndeliveredEmail());
        }
        if(StringUtils.isEmpty(customerName)){
            customerName = order.getCustomerName();
        }
        // Customer Name addition
        if (StringUtils.isNotBlank(customerName)) {
            order.setCustomerName(customerName);
        } else if(Objects.nonNull(customer)){
            order.setCustomerName(customer.getId() > 5 ? customer.getFirstName() : null);
        }
        // Customer address clear out
        if(Objects.nonNull(customer)) {
            Address a = null;
            if (order.getDeliveryAddress() != null) {
                for (Address address : customer.getAddresses()) {
                    if (order.getDeliveryAddress().equals(address.getId())) {
                        a = address;
                    }
                }
            }
            if (!CollectionUtils.isEmpty(customer.getAddresses())) {
                customer.getAddresses().clear();
            }
            if (a != null) {
                customer.getAddresses().add(a);
                if (a.getName() != null) {
                    order.setCustomerName(a.getName());
                }
            }
        }

        OrderInfo info = new OrderInfo(properties.getEnvironmentType(), order, customer, unit,
                unitCacheService.getDeliveryPartnerById(order.getDeliveryPartner()),
                unitCacheService.getChannelPartnerById(order.getChannelPartner()));
        info.setBrand(brandMetaDataCache.getBrandMetaData().get(order.getBrandId()));
        if (Objects.nonNull(order.getInvoice())) {
            info.setOrderInvoice(order.getInvoice());
        }
        return info;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo processDohfulOrder(OrderDomain orderDetail) throws DataNotFoundException, TemplateRenderingException,
            CardValidationException, DataUpdationException, JMSException, OfferValidationException {
        String randomString = orderDetail.getOrder().getGenerateOrderId();
        OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
        if (Objects.nonNull(orderDetail.getOrder())) {
            for (OrderItem orderItem : orderDetail.getOrder().getOrders()) {
                Product product = productCache.getProductById(orderItem.getProductId());
                if (Objects.nonNull(product)) {
                    orderItem.setProductName(product.getName());
                }
            }
        }
        Order order = validateOrder(orderDetail, orderUnitMapping);
        long time = System.currentTimeMillis();
        OrderInfo info = processPlainOrder(order, orderDetail.isIncludeReceipts(), orderDetail.isAddMetadata(),
                orderDetail.getOrderNotification(), randomString, orderUnitMapping);
        if (orderDetail.isIncludeReceipts()) {
            OrderInfo receiptInfo = generateReciept(info);
            info.setReceipts(receiptInfo.getReceipts());
            info.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
            info.setAndroidReceipts(receiptInfo.getAndroidReceipts());
        }
        log.info("Full Order Processing Time for {} :: {} millisecond ", info.getOrder().getGenerateOrderId(),
                System.currentTimeMillis() - time);
        if (MapUtils.isEmpty(info.getOrderNotificationMap())) {
            info.setOrderNotificationMap(new HashMap<>());
        }
        info.getOrderNotificationMap().put(info.getOrder().getOrderId(), info.getOrderNotification());
        return info;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo createSubscriptionOrderDineInApp(OrderDomain orderDomain) throws DataNotFoundException, DataUpdationException, CardValidationException, JMSException, OfferValidationException, TemplateRenderingException {
            log.info("order data object "+ orderDomain);
            String randomString =orderDomain.getOrder().getGenerateOrderId();
            OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
            Order order = validateOrder(orderDomain, orderUnitMapping);
            return createSubscrptionOrder(order,orderDomain.getWalletOrder(),orderDomain,randomString,orderUnitMapping);
    }

    private Boolean revalidatingCreateOrder(Order order) {
        try {
            long startTime = System.currentTimeMillis();
            if (props.isRevalidationOrderActive() && props.revalidationOrderPartnerIds().contains(order.getChannelPartner()) &&
                    Objects.nonNull(order.getRevalidate()) && order.getRevalidate() && !StringUtils.isEmpty(order.getRevalidationReason())) {
                log.info("Order Request object for revalidation::::: {}", new Gson().toJson(order));
                String status = RevalidateHelper.revalidate(RevalidationReason.valueOf(order.getRevalidationReason()), order, revalidationService);
                log.info("Revalidation of Order took time in ms :::::: {}" ,System.currentTimeMillis() - startTime);
                if (AppConstants.REVALIDATION_SUCCESSFUL.equalsIgnoreCase(status)) {
                    return true;
                } else {
                    taskExecutor.execute(() -> {
						sendEmailAfterRevalidationFailure(order, order.getRevalidationReason(), order.getOfferCode() + "_" + order.getCustomerId());
                        log.info("Email sent for order revalidation failure for order id ::::: {}", order.getOrderId());
                    });
                    return false;
                }
            }
        } catch (Exception e) {
            LOG.info("Error while re-validating offer applied  ::::: {}", e);
        }
        return true;
    }

    public void sendEmailAfterRevalidationFailure(Order order, String failureReason, String key) {
        try {
            LOG.info("Sending email for Order Revalidation failure");
            List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
            String revalidationFailedReason = "Revalidation Failed";
            switch (RevalidationReason.valueOf(failureReason)) {
                case COUPON_MAX_USAGE_AND_BY_CUSTOMER:
                    revalidationFailedReason = "Coupon/Customer Max usage reached";
                default:
                    break;
            }


            byte[] byteArray = null;
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(new Gson().toJson(order));
            oos.flush();
            byteArray = bos.toByteArray();
            List<AttachmentData> attachments = new ArrayList<>();
            AttachmentData fullfillmentReport = null;
            fullfillmentReport = new AttachmentData(byteArray, "OrderData", AppConstants.JSON_MIME_TYPE);
            attachments.add(fullfillmentReport);

            OfferRevalidationFailedTemplate orderRevalidationFailedTemplate = new OfferRevalidationFailedTemplate(props.getEnvironmentType(),
                    props.getBasePath(), order.getCustomerId().toString(),order.getOfferCode(),String.valueOf(order.getUnitId()),revalidationFailedReason);
            OrderRevalidationFailedNotification orderRevalidationFailedNotification = new OrderRevalidationFailedNotification(props.getEnvironmentType(),
                    toEmails, order.getCustomerId().toString(), revalidationFailedReason, orderRevalidationFailedTemplate);
            orderRevalidationFailedNotification.sendRawMailWithInLineImage(attachments,"src\\main\\resources\\images\\AlertImage.png");
        } catch (Exception e) {
            LOG.info("Error while sending email after order revalidation failure :::::: {}" + e.getMessage(),e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void addDreamFolksTransactionDetail(OrderInfo info, DreamFolksVoucherDetails dreamFolksVoucherDetails){
       try {
           if(Objects.nonNull(dreamFolksVoucherDetails) &&
                   Objects.nonNull(dreamFolksVoucherDetails.getVoucherCode()) &&
                   Objects.nonNull(dreamFolksVoucherDetails.getTransactionId()) &&
                   Objects.nonNull(dreamFolksVoucherDetails.getTransactionTime()) &&
                   Objects.nonNull(dreamFolksVoucherDetails.getRedeemedQty()) &&
                   !dreamFolksVoucherUsageCache.getDreamFolksVoucherCodesUsed().contains(dreamFolksVoucherDetails.getVoucherCode())
           ){

               DreamfolksTransactionDetail dreamfolksTransactionDetail = new DreamfolksTransactionDetail();
               dreamfolksTransactionDetail.setVoucherCode(dreamFolksVoucherDetails.getVoucherCode());
               dreamfolksTransactionDetail.setTransactionId(dreamFolksVoucherDetails.getTransactionId());
               dreamfolksTransactionDetail.setQuantityRedeemed(dreamFolksVoucherDetails.getRedeemedQty());
               dreamfolksTransactionDetail.setTransactionTime(AppUtils.getCurrentTimestamp());
               dreamfolksTransactionDetail.setOrderId(info.getOrder().getOrderId());

               dreamfolksTransactionDetailDao.save(dreamfolksTransactionDetail);
               dreamFolksVoucherUsageCache.getDreamFolksVoucherCodesUsed().add(dreamFolksVoucherDetails.getVoucherCode());
           }
       }catch (Exception e) {
           log.error("Error while adding dream folks transaction detail for order id {} : {}", info.getOrder().getOrderId(), e.getMessage());
       }
    }

    /**
     * Updates DreamFolks transaction detail orderId using voucher code
     * @param voucherCode The voucher code to update
     * @param orderId The order ID to set
     * @return true if update was successful, false otherwise
     */
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateDreamFolksOrderIdByVoucherCode(String voucherCode, Integer orderId) {
        try {
            if (Objects.isNull(voucherCode) || Objects.isNull(orderId)) {
                log.error("Voucher code or Order ID is null. Voucher Code: {}, Order ID: {}", voucherCode, orderId);
                return false;
            }

            int updatedRows = dreamfolksTransactionDetailDao.updateOrderIdByVoucherCode(voucherCode, orderId);
            
            if (updatedRows > 0) {
                log.info("Successfully updated DreamFolks transaction detail orderId for voucher code: {}, order ID: {}", 
                        voucherCode, orderId);
                return true;
            } else {
                log.warn("No DreamFolks transaction detail found for voucher code: {}", voucherCode);
                return false;
            }
        } catch (Exception e) {
            log.error("Error updating DreamFolks transaction detail orderId for voucher code: {}, order ID: {}", 
                     voucherCode, orderId, e);
            return false;
        }
    }


}

