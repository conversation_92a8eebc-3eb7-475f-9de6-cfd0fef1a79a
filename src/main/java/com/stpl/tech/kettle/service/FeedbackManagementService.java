package com.stpl.tech.kettle.service;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.Optional;
import java.util.Set;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.TableResponse;
import org.springframework.data.util.Pair;

import com.stpl.tech.kettle.data.kettle.FeedbackDetail;
import com.stpl.tech.kettle.data.kettle.FeedbackEvent;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.CreateOrderResult;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.FeedbackEventInfo;
import com.stpl.tech.kettle.domain.model.FeedbackEventStatus;
import com.stpl.tech.kettle.domain.model.FeedbackEventType;
import com.stpl.tech.kettle.domain.model.FeedbackSource;
import com.stpl.tech.kettle.domain.model.FeedbackStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.notification.ShortUrlData;

public interface FeedbackManagementService {
	void updateFeedbackUrl(int orderId, String feedbackUrl);

	void generateFeedBackEvent(int orderId, String orderSource, Date currentTimestamp, FeedbackEventType eventType,
			FeedbackDetail feedback, FeedbackSource[] eventSource);

	public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource,
			FeedbackDetail feedback, Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId);

	public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
			Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId);

	FeedbackDetail getFeedback(int orderId, FeedbackSource source, FeedbackEventType eventType);

	public FeedbackDetail generateFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
			Customer customer, Date currentTimestamp, FeedbackEventType eventType, FeedbackSource... eventSource);

	public void runNpsProc();

	public Date getTriggerTime(FeedbackSource source, String orderSource, Date currentTimestamp,
			FeedbackEventType eventType);

	public boolean cancelFeedBackforOrder(Integer orderId, FeedbackEventType nps);

	public boolean availableForNPSEvent(int customerId, Date triggerTime);

	public boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId);

	Integer getOrderId(Integer feedbackId);

	FeedbackDetail getFeedbackForSource(Integer orderId, FeedbackSource source);

	Optional<OrderDetail> getOrderDetail(Integer orderId);

	void createNPSFeedBack(Set<Integer> productIds, Customer customer, OrderDetail orderDetail, Date currentTimestamp,
			String orderFeedbackType, CreateOrderResult result, Order order, StringBuilder sb);

	FeedbackDetail createOrderFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
			Date currentTimestamp, StringBuilder sb);

	FeedbackDetail createNPSFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
			Date currentTimestamp, StringBuilder sb);

	boolean orderEligibleForInAppFeedback(Set<Integer> productIds, Order order);

	FeedbackDetail createOrderFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId, int unitId,
			String orderSource, Date currentTimestamp, StringBuilder sb);

	FeedbackDetail createNPSFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId, int unitId,
			String orderSource, Date currentTimestamp, StringBuilder sb);

	Pair<String, String> createFeedbackUrl(CreateOrderResult result);

	Pair<String, String> getFeedbackLinkForQRCode(int feedbackId, FeedbackSource feedbackSource);

	Pair<String, String> getFeedbackLinkForSource(int feedbackId, FeedbackSource feedbackSource);

	Date updateFeedbackEventStatus(int feedbackId, int eventId, ShortUrlData shortUrl, String longUrl,
			FeedbackEventStatus status, FeedbackStatus feedbackStatus);

	FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr);

	void updateLastNPSTime(Date updateTime, int customerId);

	FeedbackEventInfo getPendingNPSForCustomer(FeedbackSource source, Integer orderId, Integer customerId);

	public String getFeedbackUrl(OrderInfo info, TableResponse tableResponse) throws URISyntaxException, MalformedURLException;

}
