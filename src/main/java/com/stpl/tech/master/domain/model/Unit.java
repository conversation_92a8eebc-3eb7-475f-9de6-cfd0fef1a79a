package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Unit implements Serializable {

	private static final long serialVersionUID = 1536706683006809232L;

	private int id;

	private String name;

	private String region;

	private UnitCategory family;

	private UnitSubCategory subCategory;

	private UnitStatus status;

	private String unitEmail;

	private Date startDate;

	private Date lastBusinessDate;

	private String tin;
	private String fssai;

	private Division division;

	private Address address;
	private Integer cloneUnitId;

	private Integer inventoryCloneUnitId;

	private Integer menuSequenceCloneUnitId;

	private Integer creditAccount;

	private int noOfTerminals;

	private int noOfTakeawayTerminals;
	private boolean workstationEnabled;
	private boolean tokenEnabled;
	private boolean freeInternetAccess;
	private boolean tableService;
	private int tableServiceType;

	private int noOfTables;

	private int tokenLimit;

	private Employee unitManager;

	private List<Product> products;
	private List<PartnerDetail> deliveryPartners;

	private List<TaxProfile> taxProfiles;

	private List<Integer> paymentModes;

	private List<UnitHours> operationalHours;

	private String referenceName;

	private String channel;

	private Integer managerId;

	private String managerName;

	private String managerEmail;

	private String managerChannel;

	private String managerContact;

	private Location location;

	private boolean isPartnerPriced;

	private UnitBusinessType unitBusinessType;

	private Integer noOfMeter;
	private boolean dGAvailable;
	private Company company;
	private IdCodeName cafeManager;
	private boolean live;
	private boolean hotAndColdMerged;
	private boolean liveInventoryEnabled;
	private String packagingType;
	private BigDecimal packagingValue;

	private Date handoverDate;
	private String cafeAppStatus;
	private String cafeNeoStatus;
	private String googleMerchantId;
	private String shortName;
	private String costCenterName;
	private String shortCode;
	private Integer salesClonedFrom;
	private Date probableOpeningDate;
	private Integer pricingProfile;
	private String unitZone;
	private String f9Enabled;
	private String revenueCertificateEmail;
	private boolean revenueCertificateGenerationEnable;
	private boolean isHotspotEnabled;

	private Boolean isClosed;

	private String loyalTeaRedemptionAllowed;
	private CafeTimingTrimmedData cafeTimingTrimmedData;

	private String isSuperUEnabled;
	private Boolean customerLogin = false;
	private Boolean autoTokenEnabled = false;

	private Boolean isTestingUnit = false;

	public int getId() {
		return id;
	}

	public void setId(int value) {
		this.id = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String value) {
		this.name = value;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String value) {
		this.region = value;
	}

	public UnitCategory getFamily() {
		return family;
	}

	public void setFamily(UnitCategory value) {
		this.family = value;
	}

	public UnitSubCategory getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(UnitSubCategory value) {
		this.subCategory = value;
	}

	public UnitStatus getStatus() {
		return status;
	}

	public void setStatus(UnitStatus value) {
		this.status = value;
	}

	public String getUnitEmail() {
		return unitEmail;
	}

	public void setUnitEmail(String value) {
		this.unitEmail = value;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date value) {
		this.startDate = value;
	}

	public Date getLastBusinessDate() {
		return lastBusinessDate;
	}

	public void setLastBusinessDate(Date value) {
		this.lastBusinessDate = value;
	}

	public String getTin() {
		return tin;
	}

	public void setTin(String value) {
		this.tin = value;
	}

	public Division getDivision() {
		return division;
	}

	public void setDivision(Division value) {
		this.division = value;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address value) {
		this.address = value;
	}

	public Integer getCloneUnitId() {
		return cloneUnitId;
	}

	public void setCloneUnitId(Integer value) {
		this.cloneUnitId = value;
	}

	public Integer getCreditAccount() {
		return creditAccount;
	}

	public void setCreditAccount(Integer value) {
		this.creditAccount = value;
	}

	public Integer getInventoryCloneUnitId() {
		return inventoryCloneUnitId;
	}

	public void setInventoryCloneUnitId(Integer value) {
		this.inventoryCloneUnitId = value;
	}

	public Integer getMenuSequenceCloneUnitId() {
		return menuSequenceCloneUnitId;
	}

	public void setMenuSequenceCloneUnitId(Integer menuSequenceCloneUnitId) {
		this.menuSequenceCloneUnitId = menuSequenceCloneUnitId;
	}

	public int getNoOfTerminals() {
		return noOfTerminals;
	}

	public void setNoOfTerminals(int value) {
		this.noOfTerminals = value;
	}

	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	public void setNoOfTakeawayTerminals(int value) {
		this.noOfTakeawayTerminals = value;
	}

	public boolean isWorkstationEnabled() {
		return workstationEnabled;
	}

	public void setWorkstationEnabled(boolean value) {
		this.workstationEnabled = value;
	}

	public boolean isTableService() {
		return tableService;
	}

	public void setTableService(boolean tableService) {
		this.tableService = tableService;
	}

	public int getTableServiceType() {
		return tableServiceType;
	}

	public void setTableServiceType(int tableServiceType) {
		this.tableServiceType = tableServiceType;
	}

	public int getNoOfTables() {
		return noOfTables;
	}

	public void setNoOfTables(int value) {
		this.noOfTables = value;
	}

	public Employee getUnitManager() {
		return unitManager;
	}

	public void setUnitManager(Employee value) {
		this.unitManager = value;
	}

	public List<Product> getProducts() {
		if (products == null) {
			products = new ArrayList<Product>();
		}
		return this.products;
	}

	public List<PartnerDetail> getDeliveryPartners() {
		if (deliveryPartners == null) {
			deliveryPartners = new ArrayList<PartnerDetail>();
		}
		return this.deliveryPartners;
	}

	public List<TaxProfile> getTaxProfiles() {
		if (taxProfiles == null) {
			taxProfiles = new ArrayList<TaxProfile>();
		}
		return this.taxProfiles;
	}

	public List<Integer> getPaymentModes() {
		if (paymentModes == null) {
			paymentModes = new ArrayList<Integer>();
		}
		return this.paymentModes;
	}

	public List<UnitHours> getOperationalHours() {
		if (operationalHours == null) {
			operationalHours = new ArrayList<UnitHours>();
		}
		return this.operationalHours;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Unit other = (Unit) obj;
		if (id != other.id)
			return false;
		return true;
	}

	public boolean isFreeInternetAccess() {
		return freeInternetAccess;
	}

	public void setFreeInternetAccess(boolean freeInternetAccess) {
		this.freeInternetAccess = freeInternetAccess;
	}

	/**
	 * Gets the value of the referenceName property.
	 *
	 * @return possible object is {@link String }
	 */
	public String getReferenceName() {
		return referenceName;
	}

	public void setReferenceName(String value) {
		this.referenceName = value;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Integer getManagerId() {
		return managerId;
	}

	public void setManagerId(Integer managerId) {
		this.managerId = managerId;
	}

	public String getManagerEmail() {
		return managerEmail;
	}

	public void setManagerEmail(String managerEmail) {
		this.managerEmail = managerEmail;
	}

	public String getManagerChannel() {
		return managerChannel;
	}

	public void setManagerChannel(String managerSlack) {
		this.managerChannel = managerSlack;
	}

	public String getManagerName() {
		return managerName;
	}

	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}

	public String getManagerContact() {
		return managerContact;
	}

	public void setManagerContact(String managerContact) {
		this.managerContact = managerContact;
	}

	public Location getLocation() {
		return location;
	}

	public void setLocation(Location location) {
		this.location = location;
	}

	public boolean isPartnerPriced() {
		return isPartnerPriced;
	}

	public void setPartnerPriced(boolean isPartnerPriced) {
		this.isPartnerPriced = isPartnerPriced;
	}

	public boolean isTokenEnabled() {
		return tokenEnabled;
	}

	public void setTokenEnabled(boolean tokenEnabled) {
		this.tokenEnabled = tokenEnabled;
	}

	public int getTokenLimit() {
		return tokenLimit;
	}

	public void setTokenLimit(int tokenLimit) {
		this.tokenLimit = tokenLimit;
	}

	public UnitBusinessType getUnitBusinessType() {
		return unitBusinessType;
	}

	public void setUnitBusinessType(UnitBusinessType unitBusinessType) {
		this.unitBusinessType = unitBusinessType;
	}

	public Integer getNoOfMeter() {
		return noOfMeter;
	}

	public void setNoOfMeter(Integer noOfMeter) {
		this.noOfMeter = noOfMeter;
	}

	public boolean isdGAvailable() {
		return dGAvailable;
	}

	public void setdGAvailable(boolean dGAvailable) {
		this.dGAvailable = dGAvailable;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public IdCodeName getCafeManager() {
		return cafeManager;
	}

	public void setCafeManager(IdCodeName cafeManager) {
		this.cafeManager = cafeManager;
	}

	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}

	public boolean isHotAndColdMerged() {
		return hotAndColdMerged;
	}

	public void setHotAndColdMerged(boolean hotAndColdMerged) {
		this.hotAndColdMerged = hotAndColdMerged;
	}

	public boolean isLiveInventoryEnabled() {
		return liveInventoryEnabled;
	}

	public void setLiveInventoryEnabled(boolean liveInventoryEnabled) {
		this.liveInventoryEnabled = liveInventoryEnabled;
	}

	public String getPackagingType() {
		return packagingType;
	}

	public void setPackagingType(String packagingType) {
		this.packagingType = packagingType;
	}

	public BigDecimal getPackagingValue() {
		return packagingValue;
	}

	public void setPackagingValue(BigDecimal packagingValue) {
		this.packagingValue = packagingValue;
	}

	public Date getHandoverDate() {
		return handoverDate;
	}

	public void setHandoverDate(Date handoverDate) {
		this.handoverDate = handoverDate;
	}

	public String getCafeAppStatus() {
		return cafeAppStatus;
	}

	public void setCafeAppStatus(String cafeAppStatus) {
		this.cafeAppStatus = cafeAppStatus;
	}

	public String getCafeNeoStatus() {
		return cafeNeoStatus;
	}

	public void setCafeNeoStatus(String cafeNeoStatus) {
		this.cafeNeoStatus = cafeNeoStatus;
	}

	public String getGoogleMerchantId() {
		return googleMerchantId;
	}

	public void setGoogleMerchantId(String googleMerchantId) {
		this.googleMerchantId = googleMerchantId;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getCostCenterName() {
		return costCenterName;
	}

	public void setCostCenterName(String costCenterName) {
		this.costCenterName = costCenterName;
	}

	public String getFssai() {
		return fssai;
	}

	public void setFssai(String fssai) {
		this.fssai = fssai;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public Integer getSalesClonedFrom() {
		return salesClonedFrom;
	}

	public void setSalesClonedFrom(Integer salesClonedFrom) {
		this.salesClonedFrom = salesClonedFrom;
	}

	public Date getProbableOpeningDate() {
		return probableOpeningDate;
	}

	public void setProbableOpeningDate(Date probableOpeningDate) {
		this.probableOpeningDate = probableOpeningDate;
	}

	public Integer getPricingProfile() {
		return pricingProfile;
	}

	public void setPricingProfile(Integer pricingProfile) {
		this.pricingProfile = pricingProfile;
	}

	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	public String getF9Enabled() {
		return f9Enabled;
	}

	public void setF9Enabled(String f9Enabled) {
		this.f9Enabled = f9Enabled;
	}

	public String getRevenueCertificateEmail() {
		return revenueCertificateEmail;
	}

	public void setRevenueCertificateEmail(String revenueCertificateEmail) {
		this.revenueCertificateEmail = revenueCertificateEmail;
	}

	public boolean isRevenueCertificateGenerationEnable() {
		return revenueCertificateGenerationEnable;
	}

	public void setRevenueCertificateGenerationEnable(boolean revenueCertificateGenerationEnable) {
		this.revenueCertificateGenerationEnable = revenueCertificateGenerationEnable;
	}

	public boolean isHotspotEnabled() {
		return isHotspotEnabled;
	}

	public void setHotspotEnabled(boolean hotspotEnabled) {
		this.isHotspotEnabled = hotspotEnabled;
	}

	public Boolean getClosed() {
		return isClosed;
	}

	public void setClosed(Boolean closed) {
		isClosed = closed;
	}

	public String getLoyalTeaRedemptionAllowed() {
		return loyalTeaRedemptionAllowed;
	}

	public void setLoyalTeaRedemptionAllowed(String loyalTeaRedemptionAllowed) {
		this.loyalTeaRedemptionAllowed = loyalTeaRedemptionAllowed;
	}
	public CafeTimingTrimmedData getCafeTimingTrimmedData(){
		return cafeTimingTrimmedData;
	}
	public void setCafeTimingTrimmedData(CafeTimingTrimmedData cafeTimingTrimmedData){
		this.cafeTimingTrimmedData = cafeTimingTrimmedData;
	}

	public String getIsSuperUEnabled() {
		return isSuperUEnabled;
	}

	public void setIsSuperUEnabled(String isSuperUEnabled) {
		this.isSuperUEnabled = isSuperUEnabled;
	}

	public Boolean getCustomerLogin() {
		return customerLogin;
	}

	public void setCustomerLogin(Boolean customerLogin) {
		this.customerLogin = customerLogin;
	}

	public Boolean getAutoTokenEnabled() {
		return autoTokenEnabled;
	}

	public void setAutoTokenEnabled(Boolean autoTokenEnabled) {
		this.autoTokenEnabled = autoTokenEnabled;
	}

	public Boolean getTestingUnit() {
		return isTestingUnit;
	}
	public void setTestingUnit(Boolean testingUnit) {
		isTestingUnit = testingUnit;
	}

}
